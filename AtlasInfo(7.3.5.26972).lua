-- AUTOMATICALLY GENERATED -- But I'm not sorry.

local AtlasInfos = {
	["Interface/AdventureMap/AdventureMap"]={
		["AdventureMapLabel-Large"]={455, 63, 0.345703, 0.790039, 0.303711, 0.365234, false, false},
		["AdventureMapLabel-Small"]={181, 39, 0.783203, 0.959961, 0.166992, 0.205078, false, false},
		["AdventureMapIcon-DailyQuest"]={25, 41, 0.826172, 0.850586, 0.244141, 0.28418, false, false},
		["AdventureMapIcon-MissionCombat"]={42, 42, 0.783203, 0.824219, 0.244141, 0.285156, false, false},
		["AdventureMapIcon-Quest"]={25, 41, 0.852539, 0.876953, 0.244141, 0.28418, false, false},
		["AdventureMapIcon-SandboxQuest"]={25, 39, 0.878906, 0.90332, 0.244141, 0.282227, false, false},
		["QuestPortraitIcon-SandboxQuest"]={33, 55, 0.944336, 0.976562, 0.111328, 0.165039, false, false},
		["AdventureMapIcon-Lock"]={28, 37, 0.961914, 0.989258, 0.166992, 0.203125, false, false},
		["AdventureMapLabel-MissionTimer"]={76, 20, 0.12207, 0.196289, 0.977539, 0.99707, false, false},
		["AdventureMapLabel-QuestDiscovered"]={189, 36, 0.783203, 0.967773, 0.207031, 0.242188, false, false},
		["FXAM-QuestBang"]={34, 54, 0.963867, 0.99707, 0.*********, 0.0537109, false, false},
		["FXAM-SmallSpikeyGlow"]={40, 52, 0.28125, 0.320312, 0.948242, 0.999023, false, false},
		["Mission-FollowerPortraitFrame"]={49, 47, 0.28125, 0.329102, 0.853516, 0.899414, false, false},
		["Mission-LootBackgroundGlow"]={183, 71, 0.783203, 0.961914, 0.*********, 0.0703125, false, false},
		["FogofWar"]={341, 371, 0.*********, 0.333984, 0.430664, 0.792969, false, false},
		["AdventureMapQuest-QuestPane"]={351, 438, 0.*********, 0.34375, 0.*********, 0.428711, false, false},
		["AdventureMapQuest-RewardsPanel"]={285, 185, 0.*********, 0.279297, 0.794922, 0.975586, false, false},
		["AdventureMapQuest-ItemNameBG"]={95, 38, 0.899414, 0.992188, 0.0722656, 0.109375, false, false},
		["AdventureMapQuest-PortraitBG"]={44, 44, 0.899414, 0.942383, 0.111328, 0.154297, false, false},
		["AdventureMap-InsetMapBorder"]={446, 308, 0.345703, 0.78125, 0.*********, 0.301758, false, false},
		["AdventureMap-combatally-clouds"]={117, 95, 0.783203, 0.897461, 0.0722656, 0.165039, false, false},
		["AdventureMap-combatally-empty"]={46, 46, 0.28125, 0.326172, 0.901367, 0.946289, false, false},
		["AdventureMap-combatally-ring"]={58, 58, 0.28125, 0.337891, 0.794922, 0.851562, false, false},
		["AdventureMap-textlabelglow"]={122, 22, 0.*********, 0.120117, 0.977539, 0.999023, false, false},
	}, -- Interface/AdventureMap/AdventureMap
	["Interface/AdventureMap/AdventureMapBorder"]={
		["AdventureMap_TopBorder"]={1002, 668, 0.*********, 0.979492, 0.*********, 0.65332, false, false},
	}, -- Interface/AdventureMap/AdventureMapBorder
	["Interface/AdventureMap/AdventureMapParchmentTile"]={
		["AdventureMap_TileBg_Parchment"]={512, 512, 0, 1, 0, 1, true, true},
	}, -- Interface/AdventureMap/AdventureMapParchmentTile
	["Interface/AdventureMap/AdventureMapTileBg"]={
		["AdventureMap_TileBg"]={512, 512, 0, 1, 0, 1, true, true},
	}, -- Interface/AdventureMap/AdventureMapTileBg
	["Interface/AlliedRaces/AlliedRacesUnlockingFrame"]={
		["AlliedRace-TraitsBorder"]={35, 35, 0.74707, 0.78125, 0.*********, 0.0351562, false, false},
		["AlliedRace-UnlockingFrame-Background"]={680, 583, 0.*********, 0.665039, 0.*********, 0.570312, false, false},
		["AlliedRace-UnlockingFrame-BottomButtonsMouseOverGlow"]={34, 32, 0.783203, 0.816406, 0.*********, 0.0322266, false, false},
		["AlliedRace-UnlockingFrame-BottomButtonsSelectionGlow"]={34, 32, 0.818359, 0.851562, 0.*********, 0.0322266, false, false},
		["AlliedRace-UnlockingFrame-Female"]={35, 31, 0.853516, 0.887695, 0.*********, 0.03125, false, false},
		["AlliedRace-UnlockingFrame-GenderMouseOverGlow"]={39, 36, 0.666992, 0.705078, 0.*********, 0.0361328, false, false},
		["AlliedRace-UnlockingFrame-GenderSelectionGlow"]={39, 36, 0.707031, 0.745117, 0.*********, 0.0361328, false, false},
		["AlliedRace-UnlockingFrame-LeftRotation"]={26, 22, 0.925781, 0.951172, 0.*********, 0.0224609, false, false},
		["AlliedRace-UnlockingFrame-Male"]={35, 31, 0.889648, 0.923828, 0.*********, 0.03125, false, false},
		["AlliedRace-UnlockingFrame-RightRotation"]={26, 22, 0.953125, 0.978516, 0.*********, 0.0224609, false, false},
		["AlliedRace-UnlockingFrame-ZoomIn"]={26, 22, 0.666992, 0.692383, 0.0380859, 0.0595703, false, false},
		["AlliedRace-UnlockingFrame-ZoomOut"]={26, 22, 0.694336, 0.719727, 0.0380859, 0.0595703, false, false},
	}, -- Interface/AlliedRaces/AlliedRacesUnlockingFrame
	["Interface/AlliedRaces/AlliedRacesUnlockingFrameCrests"]={
		["AlliedRace-Crest-Highmountain"]={64, 64, 0.********, 0.253906, 0.********, 0.253906, false, false},
		["AlliedRace-Crest-Lightforge"]={64, 64, 0.261719, 0.511719, 0.********, 0.253906, false, false},
		["AlliedRace-Crest-Nightborne"]={64, 64, 0.519531, 0.769531, 0.********, 0.253906, false, false},
		["AlliedRace-Crest-Voidelf"]={64, 64, 0.********, 0.253906, 0.261719, 0.511719, false, false},
	}, -- Interface/AlliedRaces/AlliedRacesUnlockingFrameCrests
	["Interface/AlliedRaces/AlliedRacesUnlockingFrameModelBackground"]={
		["AlliedRace-UnlockingFrame-ModelBackground-Highmountain"]={314, 578, 0.*********, 0.307617, 0.*********, 0.56543, false, false},
		["AlliedRace-UnlockingFrame-ModelBackground-Nightborne"]={314, 578, 0.30957, 0.616211, 0.*********, 0.56543, false, false},
		["AlliedRace-UnlockingFrame-ModelBackground-Voidelf"]={314, 578, 0.618164, 0.924805, 0.*********, 0.56543, false, false},
	}, -- Interface/AlliedRaces/AlliedRacesUnlockingFrameModelBackground
	["Interface/AlliedRaces/AlliedRacesUnlockingFrameModelBackgroundPart2"]={
		["AlliedRace-UnlockingFrame-ModelBackground-Lightforge"]={314, 578, 0.********, 0.615234, 0.*********, 0.56543, false, false},
	}, -- Interface/AlliedRaces/AlliedRacesUnlockingFrameModelBackgroundPart2
	["Interface/AlliedRaces/AlliedRacesUnlockingFramePart2"]={
		["AlliedRace-UnlockingFrame-ModelFrame"]={328, 589, 0.********, 0.642578, 0.*********, 0.576172, false, false},
		["AlliedRace-UnlockingFrame-RaceBanner"]={130, 584, 0.646484, 0.900391, 0.*********, 0.571289, false, false},
	}, -- Interface/AlliedRaces/AlliedRacesUnlockingFramePart2
	["Interface/Artifacts/ArtifactAnim"]={
		["Artifacts-Anim-Sparks"]={680, 680, 0.*********, 0.665039, 0.*********, 0.665039, false, false},
		["Artifacts-Anim-Orb"]={256, 256, 0.666992, 0.916992, 0.333008, 0.583008, false, false},
		["Artifacts-Anim-Glowies"]={338, 338, 0.666992, 0.99707, 0.*********, 0.331055, false, false},
	}, -- Interface/Artifacts/ArtifactAnim
	["Interface/Artifacts/ArtifactAnim2"]={
		["Artifacts-Anim-CrackedGround"]={510, 510, 0.********, 0.998047, 0.********, 0.998047, false, false},
	}, -- Interface/Artifacts/ArtifactAnim2
	["Interface/Artifacts/ArtifactForge"]={
		["Forge-ColorSwatch"]={55, 55, 0.********, 0.109375, 0.899414, 0.953125, false, false},
		["Forge-ColorSwatchBorder"]={55, 55, 0.130859, 0.238281, 0.706055, 0.759766, false, false},
		["Forge-ColorSwatchSelection"]={55, 55, 0.130859, 0.238281, 0.761719, 0.81543, false, false},
		["Forge-Lock"]={55, 55, 0.130859, 0.238281, 0.817383, 0.871094, false, false},
		["ForgeBorder-CornerBottomLeft"]={64, 64, 0.********, 0.126953, 0.706055, 0.768555, false, false},
		["ForgeBorder-CornerBottomRight"]={64, 64, 0.********, 0.126953, 0.770508, 0.833008, false, false},
		["ForgeBorder-CornerTopRight"]={64, 64, 0.********, 0.126953, 0.834961, 0.897461, false, false},
		["Forge-Background"]={460, 615, 0.********, 0.900391, 0.*********, 0.601562, false, false},
		["Forge-AppearanceStrip"]={460, 103, 0.********, 0.900391, 0.603516, 0.704102, false, false},
		["Forge-ColorSwatchBackground"]={55, 55, 0.130859, 0.238281, 0.873047, 0.926758, false, false},
		["Forge-ColorSwatchHighlight"]={55, 55, 0.130859, 0.238281, 0.928711, 0.982422, false, false},
	}, -- Interface/Artifacts/ArtifactForge
	["Interface/Artifacts/ArtifactRunes"]={
		["Rune-01-dark"]={75, 77, 0.********, 0.148438, 0.********, 0.152344, false, false},
		["Rune-01-light"]={75, 77, 0.********, 0.148438, 0.15625, 0.306641, false, false},
		["Rune-02-dark"]={75, 77, 0.********, 0.148438, 0.310547, 0.460938, false, false},
		["Rune-02-light"]={75, 77, 0.********, 0.148438, 0.464844, 0.615234, false, false},
		["Rune-03-dark"]={75, 77, 0.********, 0.148438, 0.619141, 0.769531, false, false},
		["Rune-03-light"]={75, 77, 0.********, 0.148438, 0.773438, 0.923828, false, false},
		["Rune-04-dark"]={75, 77, 0.152344, 0.298828, 0.********, 0.152344, false, false},
		["Rune-04-light"]={75, 77, 0.302734, 0.449219, 0.********, 0.152344, false, false},
		["Rune-05-dark"]={75, 77, 0.453125, 0.599609, 0.********, 0.152344, false, false},
		["Rune-05-light"]={75, 77, 0.603516, 0.75, 0.********, 0.152344, false, false},
		["Rune-06-dark"]={75, 77, 0.753906, 0.900391, 0.********, 0.152344, false, false},
		["Rune-06-light"]={75, 77, 0.152344, 0.298828, 0.15625, 0.306641, false, false},
		["Rune-07-dark"]={75, 77, 0.152344, 0.298828, 0.310547, 0.460938, false, false},
		["Rune-07-light"]={75, 77, 0.152344, 0.298828, 0.464844, 0.615234, false, false},
		["Rune-08-dark"]={75, 77, 0.152344, 0.298828, 0.619141, 0.769531, false, false},
		["Rune-08-light"]={75, 77, 0.152344, 0.298828, 0.773438, 0.923828, false, false},
		["Rune-09-dark"]={75, 77, 0.302734, 0.449219, 0.15625, 0.306641, false, false},
		["Rune-09-light"]={75, 77, 0.453125, 0.599609, 0.15625, 0.306641, false, false},
		["Rune-10-dark "]={75, 77, 0.603516, 0.75, 0.15625, 0.306641, false, false},
		["Rune-10-light"]={75, 77, 0.753906, 0.900391, 0.15625, 0.306641, false, false},
		["Rune-11-dark"]={75, 77, 0.302734, 0.449219, 0.310547, 0.460938, false, false},
		["Rune-11-light"]={75, 77, 0.302734, 0.449219, 0.464844, 0.615234, false, false},
	}, -- Interface/Artifacts/ArtifactRunes
	["Interface/Artifacts/Artifacts"]={
		["Artifacts-PerkRing-GoldMedal"]={88, 88, 0.132812, 0.21875, 0.910156, 0.996094, false, false},
		["Artifacts-PerkRing-MainProc-Glow"]={120, 120, 0.*********, 0.118164, 0.458984, 0.576172, false, false},
		["Artifacts-PerkRing-MainProc"]={92, 92, 0.490234, 0.580078, 0.873047, 0.962891, false, false},
		["Artifacts-PerkRing-Small"]={60, 60, 0.*********, 0.0595703, 0.935547, 0.994141, false, false},
		["Artifacts-PointsBox"]={28, 24, 0.958008, 0.985352, 0.0712891, 0.0947266, false, false},
		["Artifacts-PerkRing-Highlight"]={46, 46, 0.742188, 0.787109, 0.849609, 0.894531, false, false},
		["ArtifactsFX-SpinningGlowys"]={70, 70, 0.742188, 0.810547, 0.682617, 0.750977, false, false},
		["ArtifactsFX-StarBurst"]={133, 133, 0.*********, 0.130859, 0.327148, 0.457031, false, false},
		["ArtifactsFX-Whirls"]={76, 76, 0.773438, 0.847656, 0.118164, 0.192383, false, false},
		["ArtifactsFX-YellowRing"]={50, 50, 0.742188, 0.791016, 0.798828, 0.847656, false, false},
		["Artifacts-PointsBoxGreen"]={28, 24, 0.220703, 0.248047, 0.910156, 0.933594, false, false},
		["ArtifactsFX-PointSideBurstLeft"]={33, 35, 0.966797, 0.999023, 0.195312, 0.229492, false, false},
		["ArtifactsFX-PointSideBurstRight"]={33, 35, 0.966797, 0.999023, 0.231445, 0.265625, false, false},
		["Relic-Arcane-Slot"]={72, 72, 0.849609, 0.919922, 0.118164, 0.188477, false, false},
		["Relic-Arcane-TraitBG"]={120, 120, 0.*********, 0.118164, 0.578125, 0.695312, false, false},
		["Relic-Arcane-TraitGlow"]={120, 120, 0.*********, 0.118164, 0.697266, 0.814453, false, false},
		["Relic-Arcane-TraitGlowRing"]={120, 120, 0.*********, 0.118164, 0.816406, 0.933594, false, false},
		["Relic-Blood-Slot"]={72, 72, 0.921875, 0.992188, 0.118164, 0.188477, false, false},
		["Relic-Blood-TraitBG"]={120, 120, 0.132812, 0.25, 0.195312, 0.3125, false, false},
		["Relic-Blood-TraitGlow"]={120, 120, 0.132812, 0.25, 0.314453, 0.431641, false, false},
		["Relic-Blood-TraitGlowRing"]={120, 120, 0.132812, 0.25, 0.433594, 0.550781, false, false},
		["Relic-Fel-Slot"]={72, 72, 0.898438, 0.96875, 0.552734, 0.623047, false, false},
		["Relic-Fel-TraitBG"]={120, 120, 0.132812, 0.25, 0.552734, 0.669922, false, false},
		["Relic-Fel-TraitGlow"]={120, 120, 0.132812, 0.25, 0.671875, 0.789062, false, false},
		["Relic-Fel-TraitGlowRing"]={120, 120, 0.132812, 0.25, 0.791016, 0.908203, false, false},
		["Relic-Fire-Slot"]={72, 72, 0.597656, 0.667969, 0.682617, 0.75293, false, false},
		["Relic-Fire-TraitBG"]={120, 120, 0.251953, 0.369141, 0.195312, 0.3125, false, false},
		["Relic-Fire-TraitGlow"]={120, 120, 0.371094, 0.488281, 0.195312, 0.3125, false, false},
		["Relic-Fire-TraitGlowRing"]={120, 120, 0.490234, 0.607422, 0.195312, 0.3125, false, false},
		["Relic-Frost-Slot"]={72, 72, 0.597656, 0.667969, 0.754883, 0.825195, false, false},
		["Relic-Frost-TraitBG"]={120, 120, 0.609375, 0.726562, 0.195312, 0.3125, false, false},
		["Relic-Frost-TraitGlow"]={120, 120, 0.728516, 0.845703, 0.195312, 0.3125, false, false},
		["Relic-Frost-TraitGlowRing"]={120, 120, 0.847656, 0.964844, 0.195312, 0.3125, false, false},
		["Relic-Holy-Slot"]={72, 72, 0.597656, 0.667969, 0.827148, 0.897461, false, false},
		["Relic-Holy-TraitBG"]={120, 120, 0.251953, 0.369141, 0.314453, 0.431641, false, false},
		["Relic-Holy-TraitGlow"]={120, 120, 0.251953, 0.369141, 0.433594, 0.550781, false, false},
		["Relic-Holy-TraitGlowRing"]={120, 120, 0.251953, 0.369141, 0.552734, 0.669922, false, false},
		["Relic-Iron-Slot"]={72, 72, 0.597656, 0.667969, 0.899414, 0.969727, false, false},
		["Relic-Iron-TraitBG"]={120, 120, 0.251953, 0.369141, 0.671875, 0.789062, false, false},
		["Relic-Iron-TraitGlow"]={120, 120, 0.251953, 0.369141, 0.791016, 0.908203, false, false},
		["Relic-Iron-TraitGlowRing"]={120, 120, 0.371094, 0.488281, 0.314453, 0.431641, false, false},
		["Relic-Life-Slot"]={72, 72, 0.669922, 0.740234, 0.682617, 0.75293, false, false},
		["Relic-Life-TraitBG"]={120, 120, 0.490234, 0.607422, 0.314453, 0.431641, false, false},
		["Relic-Life-TraitGlow"]={120, 120, 0.609375, 0.726562, 0.314453, 0.431641, false, false},
		["Relic-Life-TraitGlowRing"]={120, 120, 0.728516, 0.845703, 0.314453, 0.431641, false, false},
		["Relic-Shadow-Slot"]={72, 72, 0.669922, 0.740234, 0.754883, 0.825195, false, false},
		["Relic-Shadow-TraitBG"]={120, 120, 0.847656, 0.964844, 0.314453, 0.431641, false, false},
		["Relic-Shadow-TraitGlow"]={120, 120, 0.371094, 0.488281, 0.433594, 0.550781, false, false},
		["Relic-Shadow-TraitGlowRing"]={120, 120, 0.371094, 0.488281, 0.552734, 0.669922, false, false},
		["Relic-SlotBG"]={34, 34, 0.958008, 0.991211, 0.*********, 0.0341797, false, false},
		["Relic-Water-Slot"]={72, 72, 0.669922, 0.740234, 0.827148, 0.897461, false, false},
		["Relic-Water-TraitBG"]={120, 120, 0.371094, 0.488281, 0.671875, 0.789062, false, false},
		["Relic-Water-TraitGlow"]={120, 120, 0.371094, 0.488281, 0.791016, 0.908203, false, false},
		["Relic-Water-TraitGlowRing"]={120, 120, 0.490234, 0.607422, 0.433594, 0.550781, false, false},
		["Relic-Wind-Slot"]={72, 72, 0.669922, 0.740234, 0.899414, 0.969727, false, false},
		["Relic-Wind-TraitBG"]={120, 120, 0.609375, 0.726562, 0.433594, 0.550781, false, false},
		["Relic-Wind-TraitGlow"]={120, 120, 0.728516, 0.845703, 0.433594, 0.550781, false, false},
		["Relic-Wind-TraitGlowRing"]={120, 120, 0.847656, 0.964844, 0.433594, 0.550781, false, false},
		["AftLevelup-CloudyLineLeft"]={255, 67, 0.271484, 0.520508, 0.118164, 0.183594, false, false},
		["AftLevelup-CloudyLineRight"]={255, 67, 0.522461, 0.771484, 0.118164, 0.183594, false, false},
		["AftLevelup-Dots1"]={46, 32, 0.490234, 0.535156, 0.964844, 0.996094, false, false},
		["AftLevelup-Dots2"]={77, 45, 0.742188, 0.817383, 0.75293, 0.796875, false, false},
		["AftLevelup-GlowLine"]={322, 18, 0.597656, 0.912109, 0.663086, 0.680664, false, false},
		["AftLevelup-IconFrame"]={80, 80, 0.371094, 0.449219, 0.910156, 0.988281, false, false},
		["AftLevelup-IconGlow"]={80, 80, 0.818359, 0.896484, 0.552734, 0.630859, false, false},
		["AftLevelup-Lines1"]={108, 115, 0.490234, 0.595703, 0.552734, 0.665039, false, false},
		["AftLevelup-Lines2"]={93, 110, 0.490234, 0.581055, 0.666992, 0.774414, false, false},
		["AftLevelup-SoftCloud"]={256, 96, 0.706055, 0.956055, 0.*********, 0.0947266, false, false},
		["AftLevelup-ToastBG"]={275, 77, 0.*********, 0.269531, 0.118164, 0.193359, false, false},
		["AftLevelup-FiligreeLeft"]={138, 34, 0.8125, 0.947266, 0.682617, 0.71582, false, false},
		["AftLevelup-FiligreeRight"]={138, 34, 0.8125, 0.947266, 0.717773, 0.750977, false, false},
		["AftLevelup-WhiteIconGlow"]={97, 97, 0.490234, 0.584961, 0.776367, 0.871094, false, false},
		["AftLevelup-WhiteStarBurst"]={133, 133, 0.*********, 0.130859, 0.195312, 0.325195, false, false},
		["Artifacts-HeaderBG"]={720, 118, 0.*********, 0.704102, 0.*********, 0.116211, false, false},
		["Artifacts-PerkRing-WhiteGlow"]={50, 50, 0.0615234, 0.110352, 0.935547, 0.984375, false, false},
		["Relic-SlotBG-Glass"]={34, 34, 0.958008, 0.991211, 0.0361328, 0.0693359, false, false},
		["Artifacts-ItemIconBorder"]={86, 86, 0.251953, 0.335938, 0.910156, 0.994141, false, false},
		["Artifacts-PerkRing-Final"]={111, 111, 0.597656, 0.706055, 0.552734, 0.661133, false, false},
		["Artifacts-PerkRing-Final-Shine"]={111, 111, 0.708008, 0.816406, 0.552734, 0.661133, false, false},
	}, -- Interface/Artifacts/Artifacts
	["Interface/Artifacts/ArtifactsHorizontal"]={
		["_ForgeBorder-Top"]={256, 16, 0, 1, 0.********, 0.0664062, true, false},
		["Artifacts-CrestRune-Gold"]={203, 204, 0.********, 0.796875, 0.0742188, 0.871094, false, false},
	}, -- Interface/Artifacts/ArtifactsHorizontal
	["Interface/Artifacts/ArtifactsVertical"]={
		["!ForgeBorder-Right"]={16, 256, 0.03125, 0.53125, 0, 1, false, true},
	}, -- Interface/Artifacts/ArtifactsVertical
	["Interface/Artifacts/ArtifactUIDeathKnightFrost"]={
		["Artifacts-DeathKnightFrost-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-DeathKnightFrost-Header"]={720, 125, 0.*********, 0.704102, 0.603516, 0.725586, false, false},
		["Artifacts-DeathKnightFrost-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-DeathKnightFrost-BG-Rune"]={204, 204, 0.*********, 0.200195, 0.727539, 0.926758, false, false},
		["Artifacts-DeathKnightFrost-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIDeathKnightFrost
	["Interface/Artifacts/ArtifactUIDemonHunter"]={
		["Artifacts-DemonHunter-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-DemonHunter-Header"]={896, 126, 0.*********, 0.875977, 0.603516, 0.726562, false, false},
		["Artifacts-DemonHunter-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-DemonHunter-BG-rune"]={270, 270, 0.*********, 0.264648, 0.728516, 0.992188, false, false},
		["Artifacts-DemonHunter-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIDemonHunter
	["Interface/Artifacts/ArtifactUIDruid"]={
		["Artifacts-Druid-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Druid-Header"]={896, 180, 0.*********, 0.875977, 0.603516, 0.779297, false, false},
		["Artifacts-Druid-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Druid-BG-rune"]={220, 220, 0.*********, 0.21582, 0.78125, 0.996094, false, false},
		["Artifacts-Druid-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIDruid
	["Interface/Artifacts/ArtifactUIHunter"]={
		["Artifacts-Hunter-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Hunter-Header"]={720, 65, 0.*********, 0.704102, 0.603516, 0.666992, false, false},
		["Artifacts-Hunter-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Hunter-BG-rune"]={210, 210, 0.*********, 0.206055, 0.668945, 0.874023, false, false},
		["Artifacts-Hunter-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIHunter
	["Interface/Artifacts/ArtifactUIMageArcane"]={
		["Artifacts-MageArcane-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-MageArcane-Header"]={720, 138, 0.*********, 0.704102, 0.603516, 0.738281, false, false},
		["Artifacts-MageArcane-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-MageArcane-BG-rune"]={204, 204, 0.*********, 0.200195, 0.740234, 0.939453, false, false},
		["Artifacts-MageArcane-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIMageArcane
	["Interface/Artifacts/ArtifactUIMonk"]={
		["Artifacts-Monk-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Monk-Header"]={896, 145, 0.*********, 0.875977, 0.603516, 0.745117, false, false},
		["Artifacts-Monk-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Monk-BG-rune"]={226, 226, 0.*********, 0.22168, 0.74707, 0.967773, false, false},
		["Artifacts-Monk-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIMonk
	["Interface/Artifacts/ArtifactUIPaladin"]={
		["Artifacts-Paladin-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Paladin-Header"]={720, 83, 0.*********, 0.704102, 0.603516, 0.68457, false, false},
		["Artifacts-Paladin-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Paladin-BG-rune"]={204, 204, 0.*********, 0.200195, 0.686523, 0.885742, false, false},
		["Artifacts-Paladin-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIPaladin
	["Interface/Artifacts/ArtifactUIPriest"]={
		["Artifacts-Priest-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Priest-Header"]={720, 110, 0.*********, 0.704102, 0.603516, 0.710938, false, false},
		["Artifacts-Priest-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Priest-BG-rune"]={246, 246, 0.*********, 0.241211, 0.712891, 0.953125, false, false},
		["Artifacts-Priest-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIPriest
	["Interface/Artifacts/ArtifactUIPriestShadow"]={
		["Artifacts-PriestShadow-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-PriestShadow-Header"]={720, 114, 0.*********, 0.704102, 0.603516, 0.714844, false, false},
		["Artifacts-PriestShadow-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-PriestShadow-BG-rune"]={246, 246, 0.*********, 0.241211, 0.716797, 0.957031, false, false},
		["Artifacts-PriestShadow-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIPriestShadow
	["Interface/Artifacts/ArtifactUIRogue"]={
		["Artifacts-Rogue-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Rogue-Header"]={720, 79, 0.*********, 0.704102, 0.603516, 0.680664, false, false},
		["Artifacts-Rogue-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Rogue-BG-rune"]={204, 204, 0.*********, 0.200195, 0.682617, 0.881836, false, false},
		["Artifacts-Rogue-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIRogue
	["Interface/Artifacts/ArtifactUIShadow"]={
		["Artifacts-BG-Shadow"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
	}, -- Interface/Artifacts/ArtifactUIShadow
	["Interface/Artifacts/ArtifactUIShaman"]={
		["Artifacts-Shaman-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Shaman-Header"]={720, 99, 0.*********, 0.704102, 0.603516, 0.700195, false, false},
		["Artifacts-Shaman-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Shaman-BG-rune"]={204, 204, 0.*********, 0.200195, 0.702148, 0.901367, false, false},
		["Artifacts-Shaman-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIShaman
	["Interface/Artifacts/ArtifactUIWarlock"]={
		["Artifacts-Warlock-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Warlock-Header"]={720, 92, 0.*********, 0.704102, 0.603516, 0.693359, false, false},
		["Artifacts-Warlock-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Warlock-BG-rune"]={204, 204, 0.*********, 0.200195, 0.695312, 0.894531, false, false},
		["Artifacts-Warlock-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIWarlock
	["Interface/Artifacts/ArtifactUIWarrior"]={
		["Artifacts-Warrior-BG"]={896, 615, 0.*********, 0.875977, 0.*********, 0.601562, false, false},
		["Artifacts-Warrior-Header"]={720, 62, 0.276367, 0.979492, 0.603516, 0.664062, false, false},
		["Artifacts-Warrior-KnowledgeRank"]={57, 56, 0.87793, 0.933594, 0.0869141, 0.141602, false, false},
		["Artifacts-Warrior-BG-rune"]={280, 280, 0.*********, 0.274414, 0.603516, 0.876953, false, false},
		["Artifacts-Warrior-FinalIcon"]={86, 86, 0.87793, 0.961914, 0.*********, 0.0849609, false, false},
	}, -- Interface/Artifacts/ArtifactUIWarrior
	["Interface/Artifacts/RelicForge"]={
		["Darklink-ball"]={44, 44, 0.415039, 0.458008, 0.544922, 0.587891, false, false},
		["Darklink-glow"]={87, 101, 0.891602, 0.976562, 0.170898, 0.269531, false, false},
		["Darklink"]={61, 82, 0.584961, 0.644531, 0.734375, 0.814453, false, false},
		["Darkstone1"]={52, 43, 0.891602, 0.942383, 0.271484, 0.313477, false, false},
		["Darkstone2"]={27, 29, 0.97168, 0.998047, 0.519531, 0.547852, false, false},
		["Darkstone3"]={29, 39, 0.96875, 0.99707, 0.0458984, 0.0839844, false, false},
		["Darkstone4"]={43, 35, 0.415039, 0.457031, 0.763672, 0.797852, false, false},
		["Darkstone5"]={31, 44, 0.415039, 0.445312, 0.874023, 0.916992, false, false},
		["Darkstone6"]={47, 40, 0.802734, 0.848633, 0.410156, 0.449219, false, false},
		["Darkstone7"]={27, 26, 0.538086, 0.564453, 0.966797, 0.992188, false, false},
		["Darktrait-border"]={56, 54, 0.404297, 0.458984, 0.935547, 0.988281, false, false},
		["Lightlink-ball"]={44, 44, 0.415039, 0.458008, 0.589844, 0.632812, false, false},
		["Lightlink-glow"]={87, 101, 0.71582, 0.800781, 0.319336, 0.417969, false, false},
		["Lightlink"]={61, 82, 0.584961, 0.644531, 0.816406, 0.896484, false, false},
		["Lightstone1"]={34, 35, 0.957031, 0.990234, 0.359375, 0.393555, false, false},
		["Lightstone2"]={28, 32, 0.97168, 0.999023, 0.486328, 0.517578, false, false},
		["Lightstone3"]={31, 31, 0.96875, 0.999023, 0.125977, 0.15625, false, false},
		["Lightstone4"]={31, 29, 0.74707, 0.777344, 0.419922, 0.448242, false, false},
		["Lightstone5"]={45, 39, 0.415039, 0.458984, 0.678711, 0.716797, false, false},
		["Lightstone6"]={33, 39, 0.957031, 0.989258, 0.319336, 0.357422, false, false},
		["Lightstone7"]={38, 44, 0.415039, 0.452148, 0.71875, 0.761719, false, false},
		["Lighttrait-border"]={56, 54, 0.792969, 0.847656, 0.640625, 0.693359, false, false},
		["Darktrait-Glow"]={74, 74, 0.651367, 0.723633, 0.566406, 0.638672, false, false},
		["Lighttrait-glow"]={74, 74, 0.725586, 0.797852, 0.566406, 0.638672, false, false},
		["Mixedtrait-border"]={56, 54, 0.849609, 0.904297, 0.640625, 0.693359, false, false},
		["Relic-Rankframe-glow"]={30, 30, 0.71582, 0.745117, 0.419922, 0.449219, false, false},
		["Relic-Rankframe"]={23, 23, 0.850586, 0.873047, 0.410156, 0.432617, false, false},
		["Relic-Rankselected-circle"]={43, 43, 0.415039, 0.457031, 0.634766, 0.676758, false, false},
		["Relic-Rankselected-UpperGlow"]={151, 150, 0.610352, 0.757812, 0.170898, 0.317383, false, false},
		["Relicforge-background"]={469, 555, 0.*********, 0.458984, 0.*********, 0.542969, false, false},
		["Relicforge-Bottomframe"]={469, 16, 0.509766, 0.967773, 0.451172, 0.466797, false, false},
		["Relicforge-Bottomleft-corner"]={71, 63, 0.333008, 0.402344, 0.935547, 0.99707, false, false},
		["Relicforge-Bottomright-corner"]={71, 63, 0.72168, 0.791016, 0.640625, 0.702148, false, false},
		["Relicforge-Leftframe"]={23, 537, 0.460938, 0.483398, 0.451172, 0.975586, false, false},
		["Relicforge-Relicsbackground"]={422, 398, 0.*********, 0.413086, 0.544922, 0.933594, false, false},
		["Relicforge-Rightframe"]={23, 537, 0.485352, 0.507812, 0.451172, 0.975586, false, false},
		["Relicforge-Slot-background"]={37, 37, 0.415039, 0.451172, 0.835938, 0.87207, false, false},
		["Relicforge-Slot-frame"]={67, 67, 0.651367, 0.716797, 0.865234, 0.930664, false, false},
		["Relicforge-Topdecoration"]={261, 50, 0.*********, 0.255859, 0.935547, 0.984375, false, false},
		["Relicforge-Topframe"]={469, 16, 0.509766, 0.967773, 0.46875, 0.484375, false, false},
		["Relicforge-Topleft-corner"]={49, 68, 0.651367, 0.699219, 0.932617, 0.999023, false, false},
		["Relicforge-Topright-corner"]={49, 68, 0.72168, 0.769531, 0.776367, 0.842773, false, false},
		["Darklink-active"]={66, 84, 0.509766, 0.574219, 0.882812, 0.964844, false, false},
		["Disablelink"]={66, 84, 0.584961, 0.649414, 0.650391, 0.732422, false, false},
		["Lightlink-active"]={67, 85, 0.509766, 0.575195, 0.797852, 0.880859, false, false},
		["Darktrait-backglow"]={172, 172, 0.460938, 0.628906, 0.*********, 0.168945, false, false},
		["Lighttrait-backglow"]={172, 172, 0.630859, 0.798828, 0.*********, 0.168945, false, false},
		["Darklink-blackinside"]={66, 84, 0.584961, 0.649414, 0.566406, 0.648438, false, false},
		["Lightlink-blackinside"]={74, 91, 0.802734, 0.875, 0.319336, 0.408203, false, false},
		["Relic-Rankdisable-DarkGlow"]={151, 150, 0.460938, 0.608398, 0.170898, 0.317383, false, false},
		["ArtifactsFX-PointSideBurstLeft-Purple"]={33, 35, 0.962891, 0.995117, 0.640625, 0.674805, false, false},
		["ArtifactsFX-PointSideBurstRight-Purple"]={33, 35, 0.780273, 0.8125, 0.704102, 0.738281, false, false},
		["ArtifactsFX-SpinningGlowys-Purple"]={70, 70, 0.874023, 0.942383, 0.566406, 0.634766, false, false},
		["ArtifactsFX-StarBurst-Purple"]={133, 133, 0.460938, 0.59082, 0.319336, 0.449219, false, false},
		["ArtifactsFX-Whirls-Purple"]={76, 76, 0.669922, 0.744141, 0.486328, 0.560547, false, false},
		["ArtifactsFX-YellowRing-Purple"]={50, 50, 0.72168, 0.770508, 0.895508, 0.944336, false, false},
		["Darktrait-border-selected"]={80, 80, 0.876953, 0.955078, 0.319336, 0.397461, false, false},
		["Lighttrait-border-selected"]={80, 80, 0.509766, 0.587891, 0.486328, 0.564453, false, false},
		["Neutralstone1"]={52, 43, 0.944336, 0.995117, 0.271484, 0.313477, false, false},
		["Neutralstone2"]={27, 29, 0.509766, 0.536133, 0.966797, 0.995117, false, false},
		["Neutralstone3"]={29, 39, 0.96875, 0.99707, 0.0859375, 0.124023, false, false},
		["Neutralstone4"]={43, 35, 0.415039, 0.457031, 0.799805, 0.833984, false, false},
		["Neutralstone5"]={31, 44, 0.96875, 0.999023, 0.*********, 0.0439453, false, false},
		["Neutralstone6"]={47, 40, 0.72168, 0.767578, 0.946289, 0.985352, false, false},
		["Neutralstone7"]={27, 26, 0.962891, 0.989258, 0.676758, 0.702148, false, false},
		["Neutraltrait-border-selected"]={80, 80, 0.589844, 0.667969, 0.486328, 0.564453, false, false},
		["Neutraltrait-border"]={56, 54, 0.90625, 0.960938, 0.640625, 0.693359, false, false},
		["Neutraltrait-backglow"]={170, 169, 0.800781, 0.966797, 0.*********, 0.166016, false, false},
		["Neutraltrait-Glow"]={74, 74, 0.799805, 0.87207, 0.566406, 0.638672, false, false},
		["AftLevelup-NeutralStarBurst"]={124, 123, 0.592773, 0.713867, 0.319336, 0.439453, false, false},
		["AftLevelup-PurpleStarBurst"]={133, 133, 0.759766, 0.889648, 0.170898, 0.300781, false, false},
		["Artifacts-PerkRing-NeutralGlow"]={50, 50, 0.876953, 0.925781, 0.399414, 0.448242, false, false},
		["Artifacts-PerkRing-PurpleGlow"]={50, 50, 0.927734, 0.976562, 0.399414, 0.448242, false, false},
		["ArtifactsFX-Whirls-Neutral"]={67, 76, 0.651367, 0.716797, 0.716797, 0.791016, false, false},
		["ArtifactsFX-YellowRing-Neutral"]={50, 50, 0.72168, 0.770508, 0.844727, 0.893555, false, false},
		["Rune-01-neutral"]={67, 72, 0.651367, 0.716797, 0.792969, 0.863281, false, false},
		["Rune-01-purple"]={75, 77, 0.746094, 0.819336, 0.486328, 0.561523, false, false},
		["Rune-02-neutral"]={75, 64, 0.257812, 0.331055, 0.935547, 0.998047, false, false},
		["Rune-02-purple"]={75, 77, 0.821289, 0.894531, 0.486328, 0.561523, false, false},
		["Rune-03-neutral"]={70, 76, 0.651367, 0.719727, 0.640625, 0.714844, false, false},
		["Rune-03-purple"]={75, 77, 0.896484, 0.969727, 0.486328, 0.561523, false, false},
		["Rune-04-neutral"]={60, 77, 0.584961, 0.643555, 0.898438, 0.973633, false, false},
		["Rune-04-purple"]={75, 77, 0.509766, 0.583008, 0.566406, 0.641602, false, false},
		["Rune-05-neutral"]={52, 74, 0.944336, 0.995117, 0.566406, 0.638672, false, false},
		["Rune-05-purple"]={75, 77, 0.509766, 0.583008, 0.643555, 0.71875, false, false},
		["Rune-06-neutral"]={58, 72, 0.72168, 0.77832, 0.704102, 0.774414, false, false},
		["Rune-06-purple"]={75, 77, 0.509766, 0.583008, 0.720703, 0.795898, false, false},
	}, -- Interface/Artifacts/RelicForge
	["Interface/Artifacts/RelicForgePreviewTraits"]={
		["Relicforge-Relicsbackground-Previewtraits"]={412, 387, 0.********, 0.806641, 0.********, 0.757812, false, false},
		["Relicforge-Slot-frame-Active"]={67, 67, 0.810547, 0.941406, 0.********, 0.132812, false, false},
	}, -- Interface/Artifacts/RelicForgePreviewTraits
	["Interface/AuctionFrame/Token"]={
		["token-button-category"]={156, 20, 0.616211, 0.768555, 0.458984, 0.498047, false, false},
		["token-itemslot"]={251, 56, 0.616211, 0.861328, 0.345703, 0.455078, false, false},
		["token-choice-bnet"]={174, 174, 0.616211, 0.786133, 0.********, 0.341797, false, false},
		["token-choice-wow"]={108, 108, 0.788086, 0.893555, 0.********, 0.212891, false, false},
		["token-info-background"]={628, 510, 0.*********, 0.614258, 0.********, 0.998047, false, false},
		["token-info-arrow"]={32, 21, 0.788086, 0.819336, 0.216797, 0.257812, false, false},
	}, -- Interface/AuctionFrame/Token
	["Interface/BankFrame/Bank"]={
		["bank-slots-shadow"]={116, 327, 0.********, 0.457031, 0.********, 0.640625, false, false},
		["bank-slots"]={98, 309, 0.464844, 0.847656, 0.********, 0.605469, false, false},
	}, -- Interface/BankFrame/Bank
	["Interface/BarberShop/Barbershop"]={
		["barbershop-background"]={266, 348, 0.267578, 0.527344, 0.********, 0.681641, false, false},
		["Barbershop-Bottom"]={271, 103, 0.529297, 0.793945, 0.********, 0.203125, false, false},
		["Barbershop-Top"]={271, 102, 0.529297, 0.793945, 0.207031, 0.40625, false, false},
		["!Barbershop-MiddleTile"]={271, 128, 0.*********, 0.265625, 0, 0.25, false, true},
	}, -- Interface/BarberShop/Barbershop
	["Interface/Buttons/128RedButton"]={
		["128-RedButton-Highlight"]={441, 128, 0.********, 0.863281, 0.381836, 0.506836, false, false},
		["128-RedButton-LeftCorner-Disabled"]={292, 128, 0.********, 0.572266, 0.508789, 0.633789, false, false},
		["128-RedButton-LeftCorner-Pressed"]={292, 128, 0.********, 0.572266, 0.635742, 0.760742, false, false},
		["128-RedButton-LeftCorner"]={292, 128, 0.********, 0.572266, 0.762695, 0.887695, false, false},
		["128-RedButton-RightCorner-Disabled"]={114, 128, 0.576172, 0.798828, 0.508789, 0.633789, false, false},
		["128-RedButton-RightCorner-Pressed"]={114, 128, 0.576172, 0.798828, 0.635742, 0.760742, false, false},
		["128-RedButton-RightCorner"]={114, 128, 0.576172, 0.798828, 0.762695, 0.887695, false, false},
		["_128-RedButton-Tile-Disabled"]={64, 128, 0, 0.125, 0.*********, 0.125977, true, false},
		["_128-RedButton-Tile-Pressed"]={64, 128, 0, 0.125, 0.12793, 0.25293, true, false},
		["_128-RedButton-Tile"]={64, 128, 0, 0.125, 0.254883, 0.379883, true, false},
	}, -- Interface/Buttons/128RedButton
	["Interface/Buttons/AdventureGuideMicrobuttonAlert"]={
		["adventureguide-microbutton-alert"]={28, 28, 0.03125, 0.90625, 0.03125, 0.90625, false, false},
	}, -- Interface/Buttons/AdventureGuideMicrobuttonAlert
	["Interface/Buttons/BlueGoldButton"]={
		["UI-DialogBox-goldbutton-down-right-blue"]={32, 32, 0.519531, 0.644531, 0.0078125, 0.257812, false, false},
		["UI-DialogBox-goldbutton-down-left-blue"]={64, 32, 0.********, 0.253906, 0.0078125, 0.257812, false, false},
		["UI-DialogBox-goldbutton-up-middle-blue"]={64, 32, 0.********, 0.253906, 0.273438, 0.523438, false, false},
		["UI-DialogBox-goldbutton-up-right-blue"]={32, 32, 0.652344, 0.777344, 0.0078125, 0.257812, false, false},
		["UI-DialogBox-goldbutton-up-left-blue"]={64, 32, 0.********, 0.253906, 0.539062, 0.789062, false, false},
		["UI-DialogBox-goldbutton-down-middle-blue"]={64, 32, 0.261719, 0.511719, 0.0078125, 0.257812, false, false},
	}, -- Interface/Buttons/BlueGoldButton
	["Interface/Buttons/ListButtons"]={
		["PetList-ButtonBackground"]={209, 46, 0.********, 0.820312, 0.********, 0.183594, false, false},
		["PetList-ButtonHighlight"]={209, 46, 0.********, 0.820312, 0.191406, 0.371094, false, false},
		["PetList-ButtonSelect"]={209, 46, 0.********, 0.820312, 0.378906, 0.558594, false, false},
	}, -- Interface/Buttons/ListButtons
	["Interface/Calendar/Calendar"]={
		["Calendar_Quest"]={91, 91, 0.0078125, 0.71875, 0.0078125, 0.71875, false, false},
	}, -- Interface/Calendar/Calendar
	["Interface/Challenges/ChallengeMode"]={
		["ChallengeMode-AffixRing-Lg"]={52, 52, 0.912109, 0.962891, 0.0537109, 0.104492, false, false},
		["ChallengeMode-AffixRing-Sm"]={34, 34, 0.964844, 0.998047, 0.*********, 0.0341797, false, false},
		["ChallengeMode-DungeonIconFrame"]={52, 52, 0.912109, 0.962891, 0.*********, 0.0517578, false, false},
		["ChallengeMode-KeystoneFrame"]={398, 548, 0.133789, 0.522461, 0.390625, 0.925781, false, false},
		["ChallengeMode-KeystoneSlotBG"]={114, 114, 0.*********, 0.112305, 0.760742, 0.87207, false, false},
		["ChallengeMode-KeystoneSlotFrame"]={120, 120, 0.*********, 0.118164, 0.641602, 0.758789, false, false},
		["ChallengeMode-KeystoneSlotFrameGlow"]={120, 120, 0.*********, 0.118164, 0.522461, 0.639648, false, false},
		["ChallengeMode-MainTabBg"]={548, 397, 0.*********, 0.536133, 0.*********, 0.388672, false, false},
		["ChallengeMode-ThinDivider"]={365, 3, 0.538086, 0.894531, 0.375, 0.37793, false, false},
		["ChallengeMode-Runes-GlowLarge"]={392, 391, 0.524414, 0.907227, 0.615234, 0.99707, false, false},
		["ChallengeMode-Runes-Large"]={381, 381, 0.538086, 0.910156, 0.*********, 0.373047, false, false},
		["ChallengeMode-Runes-LineGlow"]={242, 228, 0.524414, 0.760742, 0.390625, 0.613281, false, false},
		["ChallengeMode-Runes-SmallCircleGlow"]={134, 133, 0.*********, 0.131836, 0.390625, 0.520508, false, false},
		["ChallengeMode-Runes-Shockwave"]={206, 209, 0.762695, 0.963867, 0.390625, 0.594727, false, false},
	}, -- Interface/Challenges/ChallengeMode
	["Interface/Challenges/ChallengeModeHud"]={
		["challenges-timerborder"]={184, 23, 0.*********, 0.180664, 0.802734, 0.847656, false, false},
		["challenges-blackfade"]={333, 61, 0.387695, 0.712891, 0.431641, 0.550781, false, false},
		["challenges-timerbg"]={243, 59, 0.714844, 0.952148, 0.431641, 0.546875, false, false},
		["challenges-bannershine"]={222, 106, 0.387695, 0.604492, 0.220703, 0.427734, false, false},
		["challenges-toast"]={311, 78, 0.633789, 0.9375, 0.********, 0.154297, false, false},
		["challenges-nomedal"]={9, 10, 0.*********, 0.00976562, 0.974609, 0.994141, false, false},
		["BossBanner-PortraitBorder"]={61, 61, 0.*********, 0.0605469, 0.851562, 0.970703, false, false},
		["ChallengeMode-SoftYellowGlow"]={206, 206, 0.*********, 0.202148, 0.********, 0.404297, false, false},
		["ChallengeMode-SpikeyStar"]={200, 200, 0.*********, 0.196289, 0.408203, 0.798828, false, false},
		["ChallengeMode-Timer"]={261, 87, 0.606445, 0.861328, 0.220703, 0.390625, false, false},
		["ChallengeMode-TimerBG"]={223, 11, 0.633789, 0.851562, 0.158203, 0.179688, false, false},
		["ChallengeMode-TimerFill"]={223, 11, 0.606445, 0.824219, 0.394531, 0.416016, false, false},
		["ChallengeMode-WhiteSpikeyGlow"]={186, 209, 0.204102, 0.385742, 0.********, 0.410156, false, false},
		["ChallengeMode-Chest"]={175, 130, 0.204102, 0.375, 0.414062, 0.667969, false, false},
		["ChallengeMode-icon-chest"]={19, 20, 0.182617, 0.201172, 0.802734, 0.841797, false, false},
		["ChallengeMode-icon-redline"]={19, 19, 0.0625, 0.0810547, 0.851562, 0.888672, false, false},
		["ChallengeMode-TimerBG-back"]={223, 11, 0.633789, 0.851562, 0.183594, 0.205078, false, false},
		["ChallengeMode-guild-background"]={250, 110, 0.387695, 0.631836, 0.********, 0.216797, false, false},
		["ChallengeMode-RankLineDivider"]={193, 9, 0.387695, 0.576172, 0.554688, 0.572266, false, false},
	}, -- Interface/Challenges/ChallengeModeHud
	["Interface/Challenges/ChallengeModeRunes"]={
		["ChallengeMode-Runes-BackgroundCoverGlow"]={276, 275, 0.696289, 0.96582, 0.296875, 0.56543, false, false},
		["ChallengeMode-Runes-GlowBurstLarge"]={234, 234, 0.501953, 0.730469, 0.696289, 0.924805, false, false},
		["ChallengeMode-Runes-GlowSmall"]={261, 260, 0.*********, 0.255859, 0.696289, 0.950195, false, false},
		["ChallengeMode-RuneBG"]={710, 710, 0.*********, 0.694336, 0.*********, 0.694336, false, false},
		["ChallengeMode-Runes-BackgroundBurst"]={300, 301, 0.696289, 0.989258, 0.*********, 0.294922, false, false},
		["ChallengeMode-Runes-BL-Glow"]={78, 78, 0.791992, 0.868164, 0.567383, 0.643555, false, false},
		["ChallengeMode-Runes-BR-Glow"]={78, 78, 0.870117, 0.946289, 0.567383, 0.643555, false, false},
		["ChallengeMode-Runes-InnerCircleGlow"]={211, 209, 0.732422, 0.938477, 0.696289, 0.900391, false, false},
		["ChallengeMode-Runes-L-Glow"]={78, 78, 0.732422, 0.808594, 0.902344, 0.978516, false, false},
		["ChallengeMode-Runes-R-Glow"]={78, 78, 0.810547, 0.886719, 0.902344, 0.978516, false, false},
		["ChallengeMode-Runes-Small"]={248, 248, 0.257812, 0.5, 0.696289, 0.938477, false, false},
		["ChallengeMode-Runes-T-Glow"]={78, 78, 0.888672, 0.964844, 0.902344, 0.978516, false, false},
		["ChallengeMode-Runes-CircleGlow"]={96, 96, 0.696289, 0.790039, 0.567383, 0.661133, false, false},
	}, -- Interface/Challenges/ChallengeModeRunes
	["Interface/CharacterFrame/EquipmentManager"]={
		["equipmentmanager-spec-border"]={28, 28, 0.03125, 0.90625, 0.03125, 0.90625, false, false},
	}, -- Interface/CharacterFrame/EquipmentManager
	["Interface/ChatFrame/ChatFrame"]={
		["chatframe-scrollbar"]={4, 4, 0.125, 0.625, 0.125, 0.625, false, false},
	}, -- Interface/ChatFrame/ChatFrame
	["Interface/Collections/Collections"]={
		["collections-icon-favorites"]={31, 33, 0.181641, 0.242188, 0.0136719, 0.078125, false, false},
		["collections-itemborder-collected"]={56, 56, 0.246094, 0.355469, 0.0136719, 0.123047, false, false},
		["collections-itemborder-uncollected-innerglow"]={42, 41, 0.359375, 0.441406, 0.0136719, 0.09375, false, false},
		["collections-itemborder-uncollected"]={100, 100, 0.300781, 0.496094, 0.199219, 0.394531, false, false},
		["collections-levelplate-black"]={32, 21, 0.359375, 0.421875, 0.0976562, 0.138672, false, false},
		["collections-levelplate-gold"]={32, 21, 0.445312, 0.507812, 0.0136719, 0.0546875, false, false},
		["collections-newglow"]={59, 37, 0.511719, 0.626953, 0.0136719, 0.0859375, false, false},
		["collections-slotheader"]={490, 24, 0.********, 0.958984, 0.148438, 0.195312, false, false},
		["collections-background-filagree"]={151, 109, 0.********, 0.296875, 0.199219, 0.412109, false, false},
		["collections-background-corner"]={90, 67, 0.********, 0.177734, 0.0136719, 0.144531, false, false},
		["_collections-background-line"]={512, 4, 0, 1, 0.********, 0.00976562, true, false},
		["collections-background-shadow-small"]={13, 13, 0.181641, 0.207031, 0.0820312, 0.107422, false, false},
		["collections-background-shadow-large"]={145, 147, 0.181641, 0.464844, 0.416016, 0.703125, false, false},
		["collections-watermark-toy"]={109, 110, 0.181641, 0.394531, 0.707031, 0.921875, false, false},
		["collections-watermark-heirloom"]={90, 92, 0.********, 0.177734, 0.416016, 0.595703, false, false},
		["collections-upgradeglow"]={100, 100, 0.5, 0.695312, 0.199219, 0.394531, false, false},
		["collections-upgradeglow-blue"]={100, 100, 0.699219, 0.894531, 0.199219, 0.394531, false, false},
	}, -- Interface/Collections/Collections
	["Interface/Collections/CollectionsBackgroundTile"]={
		["collections-background-tile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Collections/CollectionsBackgroundTile
	["Interface/Common/BlueMenuRing"]={
		["bluemenu-Ring"]={102, 103, 0.0078125, 0.804688, 0.0078125, 0.8125, false, false},
	}, -- Interface/Common/BlueMenuRing
	["Interface/Common/FavoritesIcon"]={
		["PetJournal-FavoritesIcon"]={25, 25, 0.03125, 0.8125, 0.03125, 0.8125, false, false},
	}, -- Interface/Common/FavoritesIcon
	["Interface/Common/InsetShadow"]={
		["insetshadow"]={510, 395, 0.********, 0.998047, 0.********, 0.773438, false, false},
	}, -- Interface/Common/InsetShadow
	["Interface/Common/Search"]={
		["search-highlight"]={126, 27, 0.********, 0.248047, 0.617188, 0.828125, false, false},
		["search-select"]={126, 27, 0.505859, 0.751953, 0.234375, 0.445312, false, false},
		["_search-rowbg"]={64, 27, 0, 0.125, 0.0078125, 0.21875, true, false},
		["search-iconframe-large"]={38, 38, 0.755859, 0.830078, 0.234375, 0.53125, false, false},
		["search-highlight-large"]={256, 47, 0.********, 0.501953, 0.234375, 0.601562, false, false},
	}, -- Interface/Common/Search
	["Interface/Common/XPBarAnim"]={
		["XPBarAnim-OrangeGain"]={64, 8, 0.3125, 0.8125, 0.328125, 0.453125, false, false},
		["XPBarAnim-OrangeSpark"]={32, 32, 0.0078125, 0.257812, 0.453125, 0.953125, false, false},
		["XPBarAnim-OrangeTrail"]={64, 8, 0.3125, 0.8125, 0.015625, 0.140625, false, false},
		["XPBarAnim-OrangeGlow"]={64, 8, 0.3125, 0.8125, 0.171875, 0.296875, false, false},
		["XPBarAnim-GlowLines"]={37, 26, 0.0078125, 0.296875, 0.015625, 0.421875, false, false},
	}, -- Interface/Common/XPBarAnim
	["Interface/ContainerFrame/Bags"]={
		["bags-newitem"]={44, 44, 0.363281, 0.535156, 0.********, 0.175781, false, false},
		["bags-junkcoin"]={20, 18, 0.863281, 0.941406, 0.28125, 0.351562, false, false},
		["bags-innerglow"]={36, 36, 0.164062, 0.304688, 0.539062, 0.679688, false, false},
		["bags-glow-purple"]={39, 39, 0.********, 0.15625, 0.539062, 0.691406, false, false},
		["bags-glow-blue"]={39, 39, 0.542969, 0.695312, 0.164062, 0.316406, false, false},
		["bags-glow-orange"]={39, 39, 0.707031, 0.859375, 0.363281, 0.515625, false, false},
		["bags-glow-green"]={39, 39, 0.703125, 0.855469, 0.********, 0.15625, false, false},
		["bags-glow-heirloom"]={39, 39, 0.703125, 0.855469, 0.164062, 0.316406, false, false},
		["bags-glow-white"]={39, 39, 0.********, 0.15625, 0.699219, 0.851562, false, false},
		["bags-glow-flash"]={90, 90, 0.********, 0.355469, 0.********, 0.355469, false, false},
		["bags-button-autosort-down"]={28, 26, 0.********, 0.113281, 0.859375, 0.960938, false, false},
		["bags-button-autosort-up"]={28, 26, 0.164062, 0.273438, 0.835938, 0.9375, false, false},
		["bags-roundhighlight"]={36, 36, 0.164062, 0.304688, 0.6875, 0.828125, false, false},
		["bags-icon-consumables"]={28, 28, 0.863281, 0.972656, 0.********, 0.113281, false, false},
		["bags-icon-equipment"]={28, 28, 0.863281, 0.972656, 0.164062, 0.273438, false, false},
		["bags-icon-tradegoods"]={28, 28, 0.867188, 0.976562, 0.363281, 0.472656, false, false},
		["bags-glow-artifact"]={39, 39, 0.542969, 0.695312, 0.********, 0.15625, false, false},
		["bags-greenarrow"]={20, 22, 0.3125, 0.390625, 0.539062, 0.625, false, false},
		["bags-icon-addslots"]={42, 42, 0.363281, 0.527344, 0.183594, 0.347656, false, false},
		["bags-static"]={178, 43, 0.********, 0.699219, 0.363281, 0.53125, false, false},
	}, -- Interface/ContainerFrame/Bags
	["Interface/DressUpFrame/DressingRoomDeathKnight"]={
		["dressingroom-background-deathknight"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomDeathKnight
	["Interface/DressUpFrame/DressingRoomDemonHunter"]={
		["dressingroom-background-demonhunter"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomDemonHunter
	["Interface/DressUpFrame/DressingRoomDruid"]={
		["dressingroom-background-druid"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomDruid
	["Interface/DressUpFrame/DressingRoomHunter"]={
		["dressingroom-background-hunter"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomHunter
	["Interface/DressUpFrame/DressingRoomMage"]={
		["dressingroom-background-mage"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomMage
	["Interface/DressUpFrame/DressingRoomMonk"]={
		["dressingroom-background-monk"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomMonk
	["Interface/DressUpFrame/DressingRoomPaladin"]={
		["dressingroom-background-paladin"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomPaladin
	["Interface/DressUpFrame/DressingRoomPriest"]={
		["dressingroom-background-priest"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomPriest
	["Interface/DressUpFrame/DressingRoomRogue"]={
		["dressingroom-background-rogue"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomRogue
	["Interface/DressUpFrame/DressingRoomShaman"]={
		["dressingroom-background-shaman"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomShaman
	["Interface/DressUpFrame/DressingRoomWarlock"]={
		["dressingroom-background-warlock"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomWarlock
	["Interface/DressUpFrame/DressingRoomWarrior"]={
		["dressingroom-background-warrior"]={478, 500, 0.********, 0.935547, 0.********, 0.978516, false, false},
	}, -- Interface/DressUpFrame/DressingRoomWarrior
	["Interface/Durability/DeathRecap"]={
		["deathrecap-icon-tombstone"]={15, 20, 0.658203, 0.6875, 0.********, 0.0820312, false, false},
		["deathrecap-iconborder"]={36, 36, 0.583984, 0.654297, 0.********, 0.144531, false, false},
		["deathrecap-background-innerglow"]={296, 254, 0.********, 0.580078, 0.********, 0.996094, false, false},
	}, -- Interface/Durability/DeathRecap
	["Interface/EncounterJournal/AdventureGuide"]={
		["adventureguide-pane-large"]={335, 337, 0.********, 0.65625, 0.320312, 0.978516, false, false},
		["adventureguide-ring"]={94, 95, 0.775391, 0.958984, 0.********, 0.1875, false, false},
		["adventureguide-pane-small"]={344, 161, 0.********, 0.673828, 0.********, 0.316406, false, false},
		["adventureguide-rewardring"]={48, 48, 0.677734, 0.771484, 0.********, 0.0957031, false, false},
		["adventureguide-redx"]={35, 34, 0.677734, 0.746094, 0.175781, 0.242188, false, false},
		["adventureguide-icon-whatsnew"]={37, 37, 0.677734, 0.75, 0.0996094, 0.171875, false, false},
	}, -- Interface/EncounterJournal/AdventureGuide
	["Interface/EncounterJournal/LootTab"]={
		["loottab-set-itemborder-green"]={41, 41, 0.171875, 0.332031, 0.0078125, 0.328125, false, false},
		["loottab-set-itemborder-purple"]={41, 41, 0.********, 0.164062, 0.0078125, 0.328125, false, false},
		["loottab-set-itemborder-blue"]={41, 41, 0.339844, 0.5, 0.0078125, 0.328125, false, false},
		["loottab-set-itemborder-artifact"]={41, 41, 0.********, 0.164062, 0.34375, 0.664062, false, false},
		["loottab-set-itemborder-white"]={41, 41, 0.339844, 0.5, 0.34375, 0.664062, false, false},
		["loottab-set-itemborder-orange"]={41, 41, 0.171875, 0.332031, 0.34375, 0.664062, false, false},
	}, -- Interface/EncounterJournal/LootTab
	["Interface/EncounterJournal/LootTabBackground"]={
		["loottab-background"]={786, 381, 0.*********, 0.768555, 0.********, 0.746094, false, false},
		["loottab-set-background"]={744, 41, 0.*********, 0.727539, 0.75, 0.830078, false, false},
	}, -- Interface/EncounterJournal/LootTabBackground
	["Interface/FontStyles/FontStyleBlueGradient"]={
		["FontStyle_BlueGradient"]={128, 128, 0, 1, 0, 1, true, true},
	}, -- Interface/FontStyles/FontStyleBlueGradient
	["Interface/FontStyles/FontStyleGarrisons"]={
		["FontStyle_Garrisons"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/FontStyles/FontStyleGarrisons
	["Interface/FontStyles/FontStyleIronHordeMetal"]={
		["FontStyle_IronHordeMetal"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/FontStyles/FontStyleIronHordeMetal
	["Interface/FontStyles/FontStyleLegion"]={
		["FontStyle_Legion"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/FontStyles/FontStyleLegion
	["Interface/FontStyles/FontStyleMetal"]={
		["FontStyle_Metal"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/FontStyles/FontStyleMetal
	["Interface/FontStyles/FontStyleParchment"]={
		["FontStyle_Parchment"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/FontStyles/FontStyleParchment
	["Interface/FrameGeneral/GeneralFrameInsetBorders"]={
		["!GeneralFrame-InsetFrame-Left"]={8, 32, 0.015625, 0.140625, 0, 1, false, true},
		["!GeneralFrame-InsetFrame-Right"]={8, 32, 0.171875, 0.296875, 0, 1, false, true},
		["GeneralFrame-HorizontalBar-Left"]={10, 19, 0.328125, 0.484375, 0.03125, 0.625, false, false},
		["GeneralFrame-HorizontalBar-Right"]={10, 19, 0.515625, 0.671875, 0.03125, 0.625, false, false},
		["GeneralFrame-InsetFrame-BottomLeft"]={8, 8, 0.328125, 0.453125, 0.6875, 0.9375, false, false},
		["GeneralFrame-InsetFrame-BottomRight"]={8, 8, 0.515625, 0.640625, 0.6875, 0.9375, false, false},
		["GeneralFrame-InsetFrame-TopLeft"]={8, 8, 0.703125, 0.828125, 0.03125, 0.28125, false, false},
		["GeneralFrame-InsetFrame-TopRight"]={8, 8, 0.859375, 0.984375, 0.03125, 0.28125, false, false},
	}, -- Interface/FrameGeneral/GeneralFrameInsetBorders
	["Interface/FrameGeneral/GeneralFrameInsetBordersHorizontal"]={
		["_GeneralFrame-HorizontalBar"]={64, 8, 0, 1, 0.03125, 0.28125, true, false},
		["_GeneralFrame-InsetFrame-Bottom"]={32, 8, 0, 0.5, 0.34375, 0.59375, true, false},
		["_GeneralFrame-InsetFrame-Top"]={32, 8, 0, 0.5, 0.65625, 0.90625, true, false},
	}, -- Interface/FrameGeneral/GeneralFrameInsetBordersHorizontal
	["Interface/FriendsFrame/FriendsList"]={
		["friendslist-categorybutton-arrow-down"]={16, 11, 0.578125, 0.609375, 0.03125, 0.375, false, false},
		["friendslist-categorybutton-arrow-right"]={11, 16, 0.578125, 0.599609, 0.4375, 0.9375, false, false},
		["friendslist-categorybutton"]={293, 26, 0.********, 0.574219, 0.03125, 0.84375, false, false},
	}, -- Interface/FriendsFrame/FriendsList
	["Interface/FriendsFrame/SocialQueuing"]={
		["socialqueuing-icon-clock"]={13, 11, 0.59375, 0.619141, 0.273438, 0.359375, false, false},
		["socialqueuing-icon-eye"]={24, 25, 0.746094, 0.792969, 0.226562, 0.421875, false, false},
		["socialqueuing-icon-group"]={29, 26, 0.746094, 0.802734, 0.0078125, 0.210938, false, false},
		["socialqueuing-row-highlight"]={301, 55, 0.********, 0.589844, 0.0078125, 0.4375, false, false},
		["socialqueuing-row-select"]={301, 55, 0.********, 0.589844, 0.453125, 0.882812, false, false},
		["socialqueuing-friendlist-summonbutton-disabled"]={24, 32, 0.59375, 0.640625, 0.0078125, 0.257812, false, false},
		["socialqueuing-friendlist-summonbutton-down"]={24, 32, 0.644531, 0.691406, 0.0078125, 0.257812, false, false},
		["socialqueuing-friendlist-summonbutton-up"]={24, 32, 0.695312, 0.742188, 0.0078125, 0.257812, false, false},
	}, -- Interface/FriendsFrame/SocialQueuing
	["Interface/FriendsFrame/SocialQueuingToast"]={
		["quickjoin-button-friendslist-down"]={32, 32, 0.59375, 0.65625, 0.015625, 0.515625, false, false},
		["quickjoin-button-friendslist-up"]={32, 32, 0.660156, 0.722656, 0.015625, 0.515625, false, false},
		["quickjoin-button-quickjoin-down"]={32, 32, 0.859375, 0.921875, 0.015625, 0.515625, false, false},
		["quickjoin-button-quickjoin-up"]={32, 32, 0.925781, 0.988281, 0.015625, 0.515625, false, false},
		["quickjoin-toast-background"]={301, 32, 0.********, 0.589844, 0.015625, 0.515625, false, false},
		["quickjoin-toast-lines"]={287, 23, 0.********, 0.5625, 0.546875, 0.90625, false, false},
		["quickjoin-button-group-down"]={32, 32, 0.726562, 0.789062, 0.015625, 0.515625, false, false},
		["quickjoin-button-group-up"]={32, 32, 0.792969, 0.855469, 0.015625, 0.515625, false, false},
	}, -- Interface/FriendsFrame/SocialQueuingToast
	["Interface/FriendsFrame/WowshareTextures"]={
		["Crop-Corner"]={10, 10, 0.480469, 0.519531, 0.707031, 0.746094, false, false},
		["Crop-Side"]={10, 10, 0.480469, 0.519531, 0.789062, 0.828125, false, false},
		["Crop-Top"]={10, 10, 0.480469, 0.519531, 0.835938, 0.875, false, false},
		["WoWShare-AchievementIcon"]={32, 32, 0.347656, 0.472656, 0.65625, 0.78125, false, false},
		["WoWShare-AddButton-Down"]={42, 43, 0.********, 0.167969, 0.65625, 0.824219, false, false},
		["WoWShare-AddButton-Up"]={42, 43, 0.175781, 0.339844, 0.65625, 0.824219, false, false},
		["WoWShare-ChatIcon"]={23, 18, 0.347656, 0.4375, 0.921875, 0.992188, false, false},
		["WoWShare-Highlight"]={38, 39, 0.********, 0.152344, 0.832031, 0.984375, false, false},
		["WoWShare-ItemQualityBorder"]={32, 32, 0.5625, 0.6875, 0.********, 0.128906, false, false},
		["WoWShare-Plus"]={32, 32, 0.695312, 0.820312, 0.********, 0.128906, false, false},
		["WoWShare-Selection"]={38, 39, 0.175781, 0.324219, 0.832031, 0.984375, false, false},
		["WoWShare-ItemIcon"]={32, 32, 0.347656, 0.472656, 0.789062, 0.914062, false, false},
		["WoWShare-ScreenshotIcon"]={32, 32, 0.828125, 0.953125, 0.********, 0.128906, false, false},
		["WoWShare-EdgeArt"]={141, 165, 0.********, 0.554688, 0.********, 0.648438, false, false},
		["WoWShare-TwitterLogo"]={14, 11, 0.480469, 0.535156, 0.65625, 0.699219, false, false},
	}, -- Interface/FriendsFrame/WowshareTextures
	["Interface/Garrison/AllianceGarrisonTier1"]={
		["Alliance_Tier1_Barracks"]={52, 54, 0.404297, 0.505859, 0.222656, 0.433594, false, false},
		["Alliance_Tier1_Professions"]={38, 43, 0.509766, 0.583984, 0.753906, 0.921875, false, false},
		["Alliance_Tier1_TownHall"]={41, 40, 0.914062, 0.994141, 0.********, 0.160156, false, false},
		["Alliance_Tier1_Mine"]={55, 46, 0.509766, 0.617188, 0.********, 0.183594, false, false},
		["Alliance_Tier1_Trading1"]={42, 43, 0.828125, 0.910156, 0.********, 0.171875, false, false},
		["Alliance_Tier1_Armory1"]={63, 79, 0.********, 0.125, 0.304688, 0.613281, false, false},
		["Alliance_Tier1_Mage1"]={53, 57, 0.271484, 0.375, 0.6875, 0.910156, false, false},
		["Alliance_Tier1_Mage2"]={58, 58, 0.271484, 0.384766, 0.222656, 0.449219, false, false},
		["Alliance_Tier1_Armory2"]={69, 75, 0.********, 0.136719, 0.********, 0.296875, false, false},
		["Alliance_Tier1_Stables1"]={59, 59, 0.140625, 0.255859, 0.511719, 0.742188, false, false},
		["Alliance_Tier1_Barracks1"]={52, 54, 0.404297, 0.505859, 0.********, 0.214844, false, false},
		["Alliance_Tier1_Stables2"]={68, 66, 0.********, 0.134766, 0.621094, 0.878906, false, false},
		["Alliance_Tier1_Lumber1"]={58, 61, 0.140625, 0.253906, 0.265625, 0.503906, false, false},
		["Alliance_Tier1_Barn2"]={49, 39, 0.728516, 0.824219, 0.********, 0.15625, false, false},
		["Alliance_Tier1_Professions2"]={38, 43, 0.601562, 0.675781, 0.191406, 0.359375, false, false},
		["Alliance_Tier1_Inn1"]={56, 57, 0.271484, 0.380859, 0.457031, 0.679688, false, false},
		["Alliance_Tier1_Farm"]={29, 37, 0.601562, 0.658203, 0.53125, 0.675781, false, false},
		["Menagery1"]={42, 39, 0.509766, 0.591797, 0.59375, 0.746094, false, false},
		["Alliance_Tier1_Arena2"]={53, 43, 0.621094, 0.724609, 0.********, 0.171875, false, false},
		["Alliance_Tier1_Lumber2"]={55, 63, 0.140625, 0.248047, 0.75, 0.996094, false, false},
		["Alliance_Tier1_Trading2"]={37, 40, 0.601562, 0.673828, 0.367188, 0.523438, false, false},
		["Alliance_Tier1_Fishing"]={36, 30, 0.404297, 0.474609, 0.847656, 0.964844, false, false},
		["Alliance_Tier1_Barn1"]={50, 48, 0.404297, 0.501953, 0.652344, 0.839844, false, false},
		["Alliance_Tier1_Inn2"]={43, 47, 0.509766, 0.59375, 0.402344, 0.585938, false, false},
		["Alliance_Tier1_Arena1"]={45, 52, 0.509766, 0.597656, 0.191406, 0.394531, false, false},
		["Alliance_Tier1_Barracks2"]={51, 52, 0.404297, 0.503906, 0.441406, 0.644531, false, false},
		["Alliance_Tier1_Workshop1"]={66, 54, 0.271484, 0.400391, 0.********, 0.214844, false, false},
		["Alliance_Tier1_Workshop2"]={65, 65, 0.140625, 0.267578, 0.********, 0.257812, false, false},
	}, -- Interface/Garrison/AllianceGarrisonTier1
	["Interface/Garrison/AllianceGarrisonTier2"]={
		["Alliance_Tier2_Arena1"]={58, 58, 0.761719, 0.875, 0.289062, 0.515625, false, false},
		["Alliance_Tier2_Arena2"]={60, 50, 0.********, 0.119141, 0.800781, 0.996094, false, false},
		["Alliance_Tier2_Armory1"]={84, 99, 0.********, 0.166016, 0.********, 0.390625, false, false},
		["Alliance_Tier2_Armory2"]={80, 101, 0.********, 0.158203, 0.398438, 0.792969, false, false},
		["Alliance_Tier2_Barn1"]={48, 48, 0.646484, 0.740234, 0.535156, 0.722656, false, false},
		["Alliance_Tier2_Barn2"]={44, 37, 0.908203, 0.994141, 0.********, 0.148438, false, false},
		["Alliance_Tier2_Barracks1"]={70, 74, 0.505859, 0.642578, 0.********, 0.292969, false, false},
		["Alliance_Tier2_Inn1"]={63, 63, 0.505859, 0.628906, 0.300781, 0.546875, false, false},
		["Alliance_Tier2_Inn2"]={69, 56, 0.505859, 0.640625, 0.554688, 0.773438, false, false},
		["Alliance_Tier2_Lumber1"]={59, 61, 0.789062, 0.904297, 0.********, 0.242188, false, false},
		["Alliance_Tier2_Lumber2"]={59, 47, 0.878906, 0.994141, 0.289062, 0.472656, false, false},
		["Alliance_Tier2_Mage1"]={72, 75, 0.341797, 0.482422, 0.617188, 0.910156, false, false},
		["Alliance_Tier2_Mage2"]={71, 71, 0.646484, 0.785156, 0.********, 0.28125, false, false},
		["Alliance_Tier2_Stables1"]={73, 76, 0.169922, 0.3125, 0.695312, 0.992188, false, false},
		["Alliance_Tier2_Stables2"]={82, 78, 0.341797, 0.501953, 0.********, 0.308594, false, false},
		["Alliance_Tier2_Trading1"]={57, 61, 0.646484, 0.757812, 0.289062, 0.527344, false, false},
		["Alliance_Tier2_Trading2"]={54, 55, 0.505859, 0.611328, 0.78125, 0.996094, false, false},
		["Alliance_Tier2_Barracks2"]={73, 75, 0.341797, 0.484375, 0.316406, 0.609375, false, false},
		["Alliance_Tier2_Workshop1"]={81, 84, 0.169922, 0.328125, 0.359375, 0.6875, false, false},
		["Alliance_Tier2_Workshop2"]={86, 89, 0.169922, 0.337891, 0.********, 0.351562, false, false},
	}, -- Interface/Garrison/AllianceGarrisonTier2
	["Interface/Garrison/AllianceGarrisonTier3"]={
		["Alliance_Tier3_Barn1"]={62, 67, 0.345703, 0.466797, 0.578125, 0.708984, false, false},
		["Alliance_Tier3_Lumber1"]={66, 67, 0.201172, 0.330078, 0.628906, 0.759766, false, false},
		["Alliance_Tier3_Mage2"]={70, 71, 0.201172, 0.337891, 0.339844, 0.478516, false, false},
		["Alliance_Tier3_Inn1"]={74, 74, 0.********, 0.146484, 0.765625, 0.910156, false, false},
		["Alliance_Tier3_Barracks1"]={92, 90, 0.431641, 0.611328, 0.********, 0.177734, false, false},
		["Alliance_Tier3_Armory2"]={76, 96, 0.********, 0.150391, 0.574219, 0.761719, false, false},
		["Alliance_Tier3_Mage1"]={72, 75, 0.201172, 0.341797, 0.189453, 0.335938, false, false},
		["Alliance_Tier3_Lumber2"]={66, 54, 0.201172, 0.330078, 0.890625, 0.996094, false, false},
		["Alliance_Tier3_Inn2"]={70, 63, 0.201172, 0.337891, 0.763672, 0.886719, false, false},
		["Alliance_Tier3_Stables1"]={117, 94, 0.********, 0.230469, 0.********, 0.185547, false, false},
		["Alliance_Tier3_Trading2"]={56, 63, 0.476562, 0.585938, 0.310547, 0.433594, false, false},
		["Alliance_Tier3_Arena1"]={66, 73, 0.201172, 0.330078, 0.482422, 0.625, false, false},
		["Alliance_Tier3_Trading1"]={65, 66, 0.345703, 0.472656, 0.310547, 0.439453, false, false},
		["Alliance_Tier3_Stables2"]={99, 92, 0.234375, 0.427734, 0.********, 0.181641, false, false},
		["Alliance_Tier3_Barn2"]={63, 67, 0.345703, 0.46875, 0.443359, 0.574219, false, false},
		["Alliance_Tier3_Armory1"]={83, 95, 0.********, 0.164062, 0.384766, 0.570312, false, false},
		["Alliance_Tier3_Arena2"]={72, 60, 0.345703, 0.486328, 0.189453, 0.306641, false, false},
		["Alliance_Tier3_Barracks2"]={100, 98, 0.********, 0.197266, 0.189453, 0.380859, false, false},
		["Alliance_Tier3_Workshop1"]={81, 78, 0.783203, 0.941406, 0.********, 0.154297, false, false},
		["Alliance_Tier3_Workshop2"]={84, 87, 0.615234, 0.779297, 0.********, 0.171875, false, false},
	}, -- Interface/Garrison/AllianceGarrisonTier3
	["Interface/Garrison/ClassHallBackground"]={
		["ClassHall_StoneFrame-BackgroundTile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/ClassHallBackground
	["Interface/Garrison/ClassHallCombatAllyBG"]={
		["ClassHall-CombatAlly"]={890, 115, 0.*********, 0.870117, 0.0078125, 0.90625, false, false},
	}, -- Interface/Garrison/ClassHallCombatAllyBG
	["Interface/Garrison/ClassHallFrame"]={
		["StoneFrameCorner-TopLeft"]={42, 40, 0.900391, 0.982422, 0.119141, 0.197266, false, false},
		["ClassHall-Circle-Mage"]={90, 88, 0.********, 0.177734, 0.822266, 0.994141, false, false},
		["_StoneFrameTile-Bottom"]={512, 28, 0, 1, 0.********, 0.0566406, true, false},
		["_StoneFrameTile-Top"]={512, 28, 0, 1, 0.0605469, 0.115234, true, false},
		["ClassHall-Circle-DeathKnight"]={90, 88, 0.********, 0.177734, 0.119141, 0.291016, false, false},
		["ClassHall-Circle-Hunter"]={90, 88, 0.********, 0.177734, 0.646484, 0.818359, false, false},
		["ClassHall-Circle-Warrior"]={90, 88, 0.181641, 0.357422, 0.646484, 0.818359, false, false},
		["ClassHall-Circle-Druid"]={90, 88, 0.********, 0.177734, 0.470703, 0.642578, false, false},
		["ClassHall-Circle-Warlock"]={90, 88, 0.181641, 0.357422, 0.470703, 0.642578, false, false},
		["ClassHall-Circle-Shaman"]={90, 88, 0.181641, 0.357422, 0.294922, 0.466797, false, false},
		["ClassHall-Circle-Rogue"]={90, 88, 0.720703, 0.896484, 0.119141, 0.291016, false, false},
		["ClassHall-Circle-Monk"]={90, 88, 0.181641, 0.357422, 0.119141, 0.291016, false, false},
		["ClassHall-Circle-DemonHunter"]={90, 88, 0.********, 0.177734, 0.294922, 0.466797, false, false},
		["ClassHall-Circle-Priest"]={90, 88, 0.541016, 0.716797, 0.119141, 0.291016, false, false},
		["ClassHall-Circle-Paladin"]={90, 88, 0.361328, 0.537109, 0.119141, 0.291016, false, false},
	}, -- Interface/Garrison/ClassHallFrame
	["Interface/Garrison/ClassHallFrame2"]={
		["!StoneFrameTile-Left"]={28, 512, 0.03125, 0.90625, 0, 1, false, true},
	}, -- Interface/Garrison/ClassHallFrame2
	["Interface/Garrison/ClassHallInternalBackground"]={
		["ClassHall_InfoBoxMission-BackgroundTile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/ClassHallInternalBackground
	["Interface/Garrison/ClassHallMissionListIcons"]={
		["ClassHall-TreasureIcon-Desaturated"]={64, 64, 0.********, 0.253906, 0.********, 0.253906, false, false},
		["ClassHall-LegendaryIcon-Desaturated"]={64, 64, 0.261719, 0.511719, 0.********, 0.253906, false, false},
		["ClassHall-QuestIcon-Desaturated"]={64, 64, 0.519531, 0.769531, 0.********, 0.253906, false, false},
		["ClassHall-BonusIcon-Desaturated"]={64, 64, 0.********, 0.253906, 0.261719, 0.511719, false, false},
		["ClassHall-CombatIcon-Desaturated"]={64, 64, 0.********, 0.253906, 0.519531, 0.769531, false, false},
	}, -- Interface/Garrison/ClassHallMissionListIcons
	["Interface/Garrison/ClassHallUI"]={
		["ClassHall_ParchmentHeaderSelect-End"]={40, 41, 0.********, 0.160156, 0.861328, 0.941406, false, false},
		["_ClassHall_ParchmentHeaderSelect-Mid"]={128, 41, 0, 0.5, 0.353516, 0.433594, true, false},
		["_ClassHall_ParchmentHeader-Mid"]={128, 41, 0, 0.5, 0.269531, 0.349609, true, false},
		["ClassHall_ParchmentHeader-End"]={40, 41, 0.539062, 0.695312, 0.59375, 0.673828, false, false},
		["ClassHall_Follower-EquipmentBG"]={78, 78, 0.539062, 0.84375, 0.4375, 0.589844, false, false},
		["ClassHall_Follower-EquipmentFrame"]={78, 78, 0.********, 0.308594, 0.705078, 0.857422, false, false},
		["ClassHall_InfoBoxMission-Corner"]={135, 135, 0.********, 0.53125, 0.4375, 0.701172, false, false},
		["_ClassHall_InfoBoxMission-Top"]={256, 135, 0, 1, 0.********, 0.265625, true, false},
		["ClassHall_ParchmentHeader-End-2"]={40, 41, 0.703125, 0.859375, 0.59375, 0.673828, false, false},
		["ClassHall_ParchmentHeaderSelect-End-2"]={40, 41, 0.316406, 0.472656, 0.705078, 0.785156, false, false},
	}, -- Interface/Garrison/ClassHallUI
	["Interface/Garrison/ClassHallUIVertical"]={
		["!ClassHall_InfoBoxMission-Left"]={135, 256, 0.********, 0.53125, 0, 1, false, true},
	}, -- Interface/Garrison/ClassHallUIVertical
	["Interface/Garrison/GarrBuildingAlchemy1AInfo"]={
		["GarrBuilding_Alchemy_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy1AInfo
	["Interface/Garrison/GarrBuildingAlchemy1AMap"]={
		["GarrBuilding_Alchemy_1_A_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy1AMap
	["Interface/Garrison/GarrBuildingAlchemy1HInfo"]={
		["GarrBuilding_Alchemy_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy1HInfo
	["Interface/Garrison/GarrBuildingAlchemy1HMap"]={
		["GarrBuilding_Alchemy_1_H_Map"]={108, 79, 0.0078125, 0.851562, 0.0078125, 0.625, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy1HMap
	["Interface/Garrison/GarrBuildingAlchemy2AInfo"]={
		["GarrBuilding_Alchemy_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy2AInfo
	["Interface/Garrison/GarrBuildingAlchemy2AMap"]={
		["GarrBuilding_Alchemy_2_A_Map"]={108, 82, 0.0078125, 0.851562, 0.0078125, 0.648438, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy2AMap
	["Interface/Garrison/GarrBuildingAlchemy2HInfo"]={
		["GarrBuilding_Alchemy_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy2HInfo
	["Interface/Garrison/GarrBuildingAlchemy2HMap"]={
		["GarrBuilding_Alchemy_2_H_Map"]={108, 79, 0.0078125, 0.851562, 0.0078125, 0.625, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy2HMap
	["Interface/Garrison/GarrBuildingAlchemy3AInfo"]={
		["GarrBuilding_Alchemy_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy3AInfo
	["Interface/Garrison/GarrBuildingAlchemy3AMap"]={
		["GarrBuilding_Alchemy_3_A_Map"]={108, 91, 0.0078125, 0.851562, 0.0078125, 0.71875, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy3AMap
	["Interface/Garrison/GarrBuildingAlchemy3HInfo"]={
		["GarrBuilding_Alchemy_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy3HInfo
	["Interface/Garrison/GarrBuildingAlchemy3HMap"]={
		["GarrBuilding_Alchemy_3_H_Map"]={108, 82, 0.0078125, 0.851562, 0.0078125, 0.648438, false, false},
	}, -- Interface/Garrison/GarrBuildingAlchemy3HMap
	["Interface/Garrison/GarrBuildingArmory1AInfo"]={
		["GarrBuilding_Armory_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory1AInfo
	["Interface/Garrison/GarrBuildingArmory1AMap"]={
		["GarrBuilding_Armory_1_A_Map"]={108, 83, 0.0078125, 0.851562, 0.0078125, 0.65625, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory1AMap
	["Interface/Garrison/GarrBuildingArmory1HInfo"]={
		["GarrBuilding_Armory_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory1HInfo
	["Interface/Garrison/GarrBuildingArmory1HMap"]={
		["GarrBuilding_Armory_1_H_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory1HMap
	["Interface/Garrison/GarrBuildingArmory2AInfo"]={
		["GarrBuilding_Armory_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory2AInfo
	["Interface/Garrison/GarrBuildingArmory2AMap"]={
		["GarrBuilding_Armory_2_A_Map"]={108, 86, 0.0078125, 0.851562, 0.0078125, 0.679688, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory2AMap
	["Interface/Garrison/GarrBuildingArmory2HInfo"]={
		["GarrBuilding_Armory_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory2HInfo
	["Interface/Garrison/GarrBuildingArmory2HMap"]={
		["GarrBuilding_Armory_2_H_Map"]={108, 86, 0.0078125, 0.851562, 0.0078125, 0.679688, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory2HMap
	["Interface/Garrison/GarrBuildingArmory3AInfo"]={
		["GarrBuilding_Armory_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory3AInfo
	["Interface/Garrison/GarrBuildingArmory3AMap"]={
		["GarrBuilding_Armory_3_A_Map"]={108, 103, 0.0078125, 0.851562, 0.0078125, 0.8125, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory3AMap
	["Interface/Garrison/GarrBuildingArmory3HInfo"]={
		["GarrBuilding_Armory_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory3HInfo
	["Interface/Garrison/GarrBuildingArmory3HMap"]={
		["GarrBuilding_Armory_3_H_Map"]={108, 98, 0.0078125, 0.851562, 0.0078125, 0.773438, false, false},
	}, -- Interface/Garrison/GarrBuildingArmory3HMap
	["Interface/Garrison/GarrBuildingBarn1AInfo"]={
		["GarrBuilding_Barn_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn1AInfo
	["Interface/Garrison/GarrBuildingBarn1AMap"]={
		["GarrBuilding_Barn_1_A_Map"]={108, 85, 0.0078125, 0.851562, 0.0078125, 0.671875, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn1AMap
	["Interface/Garrison/GarrBuildingBarn1HInfo"]={
		["GarrBuilding_Barn_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn1HInfo
	["Interface/Garrison/GarrBuildingBarn1HMap"]={
		["GarrBuilding_Barn_1_H_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn1HMap
	["Interface/Garrison/GarrBuildingBarn2AInfo"]={
		["GarrBuilding_Barn_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn2AInfo
	["Interface/Garrison/GarrBuildingBarn2AMap"]={
		["GarrBuilding_Barn_2_A_Map"]={108, 90, 0.0078125, 0.851562, 0.0078125, 0.710938, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn2AMap
	["Interface/Garrison/GarrBuildingBarn2HInfo"]={
		["GarrBuilding_Barn_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn2HInfo
	["Interface/Garrison/GarrBuildingBarn2HMap"]={
		["GarrBuilding_Barn_2_H_Map"]={108, 88, 0.0078125, 0.851562, 0.0078125, 0.695312, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn2HMap
	["Interface/Garrison/GarrBuildingBarn3AInfo"]={
		["GarrBuilding_Barn_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn3AInfo
	["Interface/Garrison/GarrBuildingBarn3AMap"]={
		["GarrBuilding_Barn_3_A_Map"]={108, 94, 0.0078125, 0.851562, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn3AMap
	["Interface/Garrison/GarrBuildingBarn3HInfo"]={
		["GarrBuilding_Barn_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn3HInfo
	["Interface/Garrison/GarrBuildingBarn3HMap"]={
		["GarrBuilding_Barn_3_H_Map"]={108, 97, 0.0078125, 0.851562, 0.0078125, 0.765625, false, false},
	}, -- Interface/Garrison/GarrBuildingBarn3HMap
	["Interface/Garrison/GarrBuildingBarracks1AInfo"]={
		["GarrBuilding_Barracks_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks1AInfo
	["Interface/Garrison/GarrBuildingBarracks1AMap"]={
		["GarrBuilding_Barracks_1_A_Map"]={108, 70, 0.0078125, 0.851562, 0.0078125, 0.554688, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks1AMap
	["Interface/Garrison/GarrBuildingBarracks1HInfo"]={
		["GarrBuilding_Barracks_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks1HInfo
	["Interface/Garrison/GarrBuildingBarracks1HMap"]={
		["GarrBuilding_Barracks_1_H_Map"]={108, 86, 0.0078125, 0.851562, 0.0078125, 0.679688, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks1HMap
	["Interface/Garrison/GarrBuildingBarracks2AInfo"]={
		["GarrBuilding_Barracks_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks2AInfo
	["Interface/Garrison/GarrBuildingBarracks2AMap"]={
		["GarrBuilding_Barracks_2_A_Map"]={108, 83, 0.0078125, 0.851562, 0.0078125, 0.65625, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks2AMap
	["Interface/Garrison/GarrBuildingBarracks2HInfo"]={
		["GarrBuilding_Barracks_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks2HInfo
	["Interface/Garrison/GarrBuildingBarracks2HMap"]={
		["GarrBuilding_Barracks_2_H_Map"]={108, 85, 0.0078125, 0.851562, 0.0078125, 0.671875, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks2HMap
	["Interface/Garrison/GarrBuildingBarracks3AInfo"]={
		["GarrBuilding_Barracks_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks3AInfo
	["Interface/Garrison/GarrBuildingBarracks3AMap"]={
		["GarrBuilding_Barracks_3_A_Map"]={108, 86, 0.0078125, 0.851562, 0.0078125, 0.679688, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks3AMap
	["Interface/Garrison/GarrBuildingBarracks3HInfo"]={
		["GarrBuilding_Barracks_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks3HInfo
	["Interface/Garrison/GarrBuildingBarracks3HMap"]={
		["GarrBuilding_Barracks_3_H_Map"]={108, 104, 0.0078125, 0.851562, 0.0078125, 0.820312, false, false},
	}, -- Interface/Garrison/GarrBuildingBarracks3HMap
	["Interface/Garrison/GarrBuildingBlacksmith1AInfo"]={
		["GarrBuilding_Blacksmith_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith1AInfo
	["Interface/Garrison/GarrBuildingBlacksmith1AMap"]={
		["GarrBuilding_Blacksmith_1_A_Map"]={108, 74, 0.0078125, 0.851562, 0.0078125, 0.585938, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith1AMap
	["Interface/Garrison/GarrBuildingBlacksmith1HInfo"]={
		["GarrBuilding_Blacksmith_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith1HInfo
	["Interface/Garrison/GarrBuildingBlacksmith1HMap"]={
		["GarrBuilding_Blacksmith_1_H_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith1HMap
	["Interface/Garrison/GarrBuildingBlacksmith2AInfo"]={
		["GarrBuilding_Blacksmith_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith2AInfo
	["Interface/Garrison/GarrBuildingBlacksmith2AMap"]={
		["GarrBuilding_Blacksmith_2_A_Map"]={108, 75, 0.0078125, 0.851562, 0.0078125, 0.59375, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith2AMap
	["Interface/Garrison/GarrBuildingBlacksmith2HInfo"]={
		["GarrBuilding_Blacksmith_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith2HInfo
	["Interface/Garrison/GarrBuildingBlacksmith2HMap"]={
		["GarrBuilding_Blacksmith_2_H_Map"]={108, 77, 0.0078125, 0.851562, 0.0078125, 0.609375, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith2HMap
	["Interface/Garrison/GarrBuildingBlacksmith3AInfo"]={
		["GarrBuilding_Blacksmith_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith3AInfo
	["Interface/Garrison/GarrBuildingBlacksmith3AMap"]={
		["GarrBuilding_Blacksmith_3_A_Map"]={108, 76, 0.0078125, 0.851562, 0.0078125, 0.601562, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith3AMap
	["Interface/Garrison/GarrBuildingBlacksmith3HInfo"]={
		["GarrBuilding_Blacksmith_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith3HInfo
	["Interface/Garrison/GarrBuildingBlacksmith3HMap"]={
		["GarrBuilding_Blacksmith_3_H_Map"]={108, 80, 0.0078125, 0.851562, 0.0078125, 0.632812, false, false},
	}, -- Interface/Garrison/GarrBuildingBlacksmith3HMap
	["Interface/Garrison/GarrBuildingEmptyPlotLgAInfo"]={
		["GarrBuilding_EmptyPlot_A_3"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEmptyPlotLgAInfo
	["Interface/Garrison/GarrBuildingEmptyPlotLgHInfo"]={
		["GarrBuilding_EmptyPlot_H_3"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEmptyPlotLgHInfo
	["Interface/Garrison/GarrBuildingEmptyPlotMdAInfo"]={
		["GarrBuilding_EmptyPlot_A_2"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEmptyPlotMdAInfo
	["Interface/Garrison/GarrBuildingEmptyPlotMdHInfo"]={
		["GarrBuilding_EmptyPlot_H_2"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEmptyPlotMdHInfo
	["Interface/Garrison/GarrBuildingEmptyPlotSmAInfo"]={
		["GarrBuilding_EmptyPlot_A_1"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEmptyPlotSmAInfo
	["Interface/Garrison/GarrBuildingEmptyPlotSmHInfo"]={
		["GarrBuilding_EmptyPlot_H_1"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEmptyPlotSmHInfo
	["Interface/Garrison/GarrBuildingEnchanting1AInfo"]={
		["GarrBuilding_Enchanting_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting1AInfo
	["Interface/Garrison/GarrBuildingEnchanting1AMap"]={
		["GarrBuilding_Enchanting_1_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting1AMap
	["Interface/Garrison/GarrBuildingEnchanting1HInfo"]={
		["GarrBuilding_Enchanting_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting1HInfo
	["Interface/Garrison/GarrBuildingEnchanting1HMap"]={
		["GarrBuilding_Enchanting_1_H_Map"]={108, 82, 0.0078125, 0.851562, 0.0078125, 0.648438, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting1HMap
	["Interface/Garrison/GarrBuildingEnchanting2AInfo"]={
		["GarrBuilding_Enchanting_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting2AInfo
	["Interface/Garrison/GarrBuildingEnchanting2AMap"]={
		["GarrBuilding_Enchanting_2_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting2AMap
	["Interface/Garrison/GarrBuildingEnchanting2HInfo"]={
		["GarrBuilding_Enchanting_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting2HInfo
	["Interface/Garrison/GarrBuildingEnchanting2HMap"]={
		["GarrBuilding_Enchanting_2_H_Map"]={108, 83, 0.0078125, 0.851562, 0.0078125, 0.65625, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting2HMap
	["Interface/Garrison/GarrBuildingEnchanting3AInfo"]={
		["GarrBuilding_Enchanting_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting3AInfo
	["Interface/Garrison/GarrBuildingEnchanting3AMap"]={
		["GarrBuilding_Enchanting_3_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting3AMap
	["Interface/Garrison/GarrBuildingEnchanting3HInfo"]={
		["GarrBuilding_Enchanting_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting3HInfo
	["Interface/Garrison/GarrBuildingEnchanting3HMap"]={
		["GarrBuilding_Enchanting_3_H_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingEnchanting3HMap
	["Interface/Garrison/GarrBuildingEngineering1AInfo"]={
		["GarrBuilding_Engineering_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering1AInfo
	["Interface/Garrison/GarrBuildingEngineering1AMap"]={
		["GarrBuilding_Engineering_1_A_Map"]={108, 76, 0.0078125, 0.851562, 0.0078125, 0.601562, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering1AMap
	["Interface/Garrison/GarrBuildingEngineering1HInfo"]={
		["GarrBuilding_Engineering_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering1HInfo
	["Interface/Garrison/GarrBuildingEngineering1HMap"]={
		["GarrBuilding_Engineering_1_H_Map"]={108, 79, 0.0078125, 0.851562, 0.0078125, 0.625, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering1HMap
	["Interface/Garrison/GarrBuildingEngineering2AInfo"]={
		["GarrBuilding_Engineering_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering2AInfo
	["Interface/Garrison/GarrBuildingEngineering2AMap"]={
		["GarrBuilding_Engineering_2_A_Map"]={108, 75, 0.0078125, 0.851562, 0.0078125, 0.59375, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering2AMap
	["Interface/Garrison/GarrBuildingEngineering2HInfo"]={
		["GarrBuilding_Engineering_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering2HInfo
	["Interface/Garrison/GarrBuildingEngineering2HMap"]={
		["GarrBuilding_Engineering_2_H_Map"]={108, 79, 0.0078125, 0.851562, 0.0078125, 0.625, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering2HMap
	["Interface/Garrison/GarrBuildingEngineering3AInfo"]={
		["GarrBuilding_Engineering_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering3AInfo
	["Interface/Garrison/GarrBuildingEngineering3AMap"]={
		["GarrBuilding_Engineering_3_A_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering3AMap
	["Interface/Garrison/GarrBuildingEngineering3HInfo"]={
		["GarrBuilding_Engineering_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering3HInfo
	["Interface/Garrison/GarrBuildingEngineering3HMap"]={
		["GarrBuilding_Engineering_3_H_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingEngineering3HMap
	["Interface/Garrison/GarrBuildingFarm1AInfo"]={
		["GarrBuilding_Farm_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm1AInfo
	["Interface/Garrison/GarrBuildingFarm1AMap"]={
		["GarrBuilding_Farm_1_A_Map"]={108, 98, 0.0078125, 0.851562, 0.0078125, 0.773438, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm1AMap
	["Interface/Garrison/GarrBuildingFarm1HInfo"]={
		["GarrBuilding_Farm_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm1HInfo
	["Interface/Garrison/GarrBuildingFarm1HMap"]={
		["GarrBuilding_Farm_1_H_Map"]={108, 94, 0.0078125, 0.851562, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm1HMap
	["Interface/Garrison/GarrBuildingFarm2AInfo"]={
		["GarrBuilding_Farm_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm2AInfo
	["Interface/Garrison/GarrBuildingFarm2AMap"]={
		["GarrBuilding_Farm_2_A_Map"]={108, 98, 0.0078125, 0.851562, 0.0078125, 0.773438, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm2AMap
	["Interface/Garrison/GarrBuildingFarm3AInfo"]={
		["GarrBuilding_Farm_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm3AInfo
	["Interface/Garrison/GarrBuildingFarm3AMap"]={
		["GarrBuilding_Farm_3_A_Map"]={108, 98, 0.0078125, 0.851562, 0.0078125, 0.773438, false, false},
	}, -- Interface/Garrison/GarrBuildingFarm3AMap
	["Interface/Garrison/GarrBuildingFishing1AInfo"]={
		["GarrBuilding_Fishing_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing1AInfo
	["Interface/Garrison/GarrBuildingFishing1AMap"]={
		["GarrBuilding_Fishing_1_A_Map"]={108, 74, 0.0078125, 0.851562, 0.0078125, 0.585938, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing1AMap
	["Interface/Garrison/GarrBuildingFishing1HInfo"]={
		["GarrBuilding_Fishing_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing1HInfo
	["Interface/Garrison/GarrBuildingFishing1HMap"]={
		["GarrBuilding_Fishing_1_H_Map"]={108, 80, 0.0078125, 0.851562, 0.0078125, 0.632812, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing1HMap
	["Interface/Garrison/GarrBuildingFishing2AInfo"]={
		["GarrBuilding_Fishing_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing2AInfo
	["Interface/Garrison/GarrBuildingFishing2AMap"]={
		["GarrBuilding_Fishing_2_A_Map"]={108, 75, 0.0078125, 0.851562, 0.0078125, 0.59375, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing2AMap
	["Interface/Garrison/GarrBuildingFishing2HInfo"]={
		["GarrBuilding_Fishing_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing2HInfo
	["Interface/Garrison/GarrBuildingFishing2HMap"]={
		["GarrBuilding_Fishing_2_H_Map"]={108, 84, 0.0078125, 0.851562, 0.0078125, 0.664062, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing2HMap
	["Interface/Garrison/GarrBuildingFishing3AInfo"]={
		["GarrBuilding_Fishing_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing3AInfo
	["Interface/Garrison/GarrBuildingFishing3AMap"]={
		["GarrBuilding_Fishing_3_A_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing3AMap
	["Interface/Garrison/GarrBuildingFishing3HInfo"]={
		["GarrBuilding_Fishing_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing3HInfo
	["Interface/Garrison/GarrBuildingFishing3HMap"]={
		["GarrBuilding_Fishing_3_H_Map"]={108, 80, 0.0078125, 0.851562, 0.0078125, 0.632812, false, false},
	}, -- Interface/Garrison/GarrBuildingFishing3HMap
	["Interface/Garrison/GarrBuildingInn1AInfo"]={
		["GarrBuilding_Inn_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInn1AInfo
	["Interface/Garrison/GarrBuildingInn1AMap"]={
		["GarrBuilding_Inn_1_A_Map"]={108, 71, 0.0078125, 0.851562, 0.0078125, 0.5625, false, false},
	}, -- Interface/Garrison/GarrBuildingInn1AMap
	["Interface/Garrison/GarrBuildingInn1HInfo"]={
		["GarrBuilding_Inn_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInn1HInfo
	["Interface/Garrison/GarrBuildingInn1HMap"]={
		["GarrBuilding_Inn_1_H_Map"]={108, 88, 0.0078125, 0.851562, 0.0078125, 0.695312, false, false},
	}, -- Interface/Garrison/GarrBuildingInn1HMap
	["Interface/Garrison/GarrBuildingInn2AInfo"]={
		["GarrBuilding_Inn_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInn2AInfo
	["Interface/Garrison/GarrBuildingInn2AMap"]={
		["GarrBuilding_Inn_2_A_Map"]={108, 79, 0.0078125, 0.851562, 0.0078125, 0.625, false, false},
	}, -- Interface/Garrison/GarrBuildingInn2AMap
	["Interface/Garrison/GarrBuildingInn2HInfo"]={
		["GarrBuilding_Inn_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInn2HInfo
	["Interface/Garrison/GarrBuildingInn2HMap"]={
		["GarrBuilding_Inn_2_H_Map"]={108, 100, 0.0078125, 0.851562, 0.0078125, 0.789062, false, false},
	}, -- Interface/Garrison/GarrBuildingInn2HMap
	["Interface/Garrison/GarrBuildingInn3AInfo"]={
		["GarrBuilding_Inn_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInn3AInfo
	["Interface/Garrison/GarrBuildingInn3AMap"]={
		["GarrBuilding_Inn_3_A_Map"]={108, 84, 0.0078125, 0.851562, 0.0078125, 0.664062, false, false},
	}, -- Interface/Garrison/GarrBuildingInn3AMap
	["Interface/Garrison/GarrBuildingInn3HInfo"]={
		["GarrBuilding_Inn_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInn3HInfo
	["Interface/Garrison/GarrBuildingInn3HMap"]={
		["GarrBuilding_Inn_3_H_Map"]={108, 116, 0.0078125, 0.851562, 0.0078125, 0.914062, false, false},
	}, -- Interface/Garrison/GarrBuildingInn3HMap
	["Interface/Garrison/GarrBuildingInscription1AInfo"]={
		["GarrBuilding_Inscription_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription1AInfo
	["Interface/Garrison/GarrBuildingInscription1AMap"]={
		["GarrBuilding_Inscription_1_A_Map"]={108, 75, 0.0078125, 0.851562, 0.0078125, 0.59375, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription1AMap
	["Interface/Garrison/GarrBuildingInscription1HInfo"]={
		["GarrBuilding_Inscription_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription1HInfo
	["Interface/Garrison/GarrBuildingInscription1HMap"]={
		["GarrBuilding_Inscription_1_H_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription1HMap
	["Interface/Garrison/GarrBuildingInscription2AInfo"]={
		["GarrBuilding_Inscription_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription2AInfo
	["Interface/Garrison/GarrBuildingInscription2AMap"]={
		["GarrBuilding_Inscription_2_A_Map"]={108, 91, 0.0078125, 0.851562, 0.0078125, 0.71875, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription2AMap
	["Interface/Garrison/GarrBuildingInscription2HInfo"]={
		["GarrBuilding_Inscription_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription2HInfo
	["Interface/Garrison/GarrBuildingInscription2HMap"]={
		["GarrBuilding_Inscription_2_H_Map"]={108, 87, 0.0078125, 0.851562, 0.0078125, 0.6875, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription2HMap
	["Interface/Garrison/GarrBuildingInscription3AInfo"]={
		["GarrBuilding_Inscription_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription3AInfo
	["Interface/Garrison/GarrBuildingInscription3AMap"]={
		["GarrBuilding_Inscription_3_A_Map"]={108, 91, 0.0078125, 0.851562, 0.0078125, 0.71875, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription3AMap
	["Interface/Garrison/GarrBuildingInscription3HInfo"]={
		["GarrBuilding_Inscription_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription3HInfo
	["Interface/Garrison/GarrBuildingInscription3HMap"]={
		["GarrBuilding_Inscription_3_H_Map"]={108, 90, 0.0078125, 0.851562, 0.0078125, 0.710938, false, false},
	}, -- Interface/Garrison/GarrBuildingInscription3HMap
	["Interface/Garrison/GarrBuildingJewelcrafting1AInfo"]={
		["GarrBuilding_Jewelcrafting_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting1AInfo
	["Interface/Garrison/GarrBuildingJewelcrafting1AMap"]={
		["GarrBuilding_Jewelcrafting_1_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting1AMap
	["Interface/Garrison/GarrBuildingJewelcrafting1HInfo"]={
		["GarrBuilding_Jewelcrafting_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting1HInfo
	["Interface/Garrison/GarrBuildingJewelcrafting1HMap"]={
		["GarrBuilding_Jewelcrafting_1_H_Map"]={108, 80, 0.0078125, 0.851562, 0.0078125, 0.632812, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting1HMap
	["Interface/Garrison/GarrBuildingJewelcrafting2AInfo"]={
		["GarrBuilding_Jewelcrafting_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting2AInfo
	["Interface/Garrison/GarrBuildingJewelcrafting2AMap"]={
		["GarrBuilding_Jewelcrafting_2_A_Map"]={108, 70, 0.0078125, 0.851562, 0.0078125, 0.554688, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting2AMap
	["Interface/Garrison/GarrBuildingJewelcrafting2HInfo"]={
		["GarrBuilding_Jewelcrafting_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting2HInfo
	["Interface/Garrison/GarrBuildingJewelcrafting2HMap"]={
		["GarrBuilding_Jewelcrafting_2_H_Map"]={108, 85, 0.0078125, 0.851562, 0.0078125, 0.671875, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting2HMap
	["Interface/Garrison/GarrBuildingJewelcrafting3AInfo"]={
		["GarrBuilding_Jewelcrafting_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting3AInfo
	["Interface/Garrison/GarrBuildingJewelcrafting3AMap"]={
		["GarrBuilding_Jewelcrafting_3_A_Map"]={108, 76, 0.0078125, 0.851562, 0.0078125, 0.601562, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting3AMap
	["Interface/Garrison/GarrBuildingJewelcrafting3HInfo"]={
		["GarrBuilding_Jewelcrafting_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting3HInfo
	["Interface/Garrison/GarrBuildingJewelcrafting3HMap"]={
		["GarrBuilding_Jewelcrafting_3_H_Map"]={108, 93, 0.0078125, 0.851562, 0.0078125, 0.734375, false, false},
	}, -- Interface/Garrison/GarrBuildingJewelcrafting3HMap
	["Interface/Garrison/GarrBuildingLeatherworking1AInfo"]={
		["GarrBuilding_Leatherworking_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking1AInfo
	["Interface/Garrison/GarrBuildingLeatherworking1AMap"]={
		["GarrBuilding_Leatherworking_1_A_Map"]={108, 70, 0.0078125, 0.851562, 0.0078125, 0.554688, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking1AMap
	["Interface/Garrison/GarrBuildingLeatherworking1HInfo"]={
		["GarrBuilding_Leatherworking_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking1HInfo
	["Interface/Garrison/GarrBuildingLeatherworking1HMap"]={
		["GarrBuilding_Leatherworking_1_H_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking1HMap
	["Interface/Garrison/GarrBuildingLeatherworking2AInfo"]={
		["GarrBuilding_Leatherworking_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking2AInfo
	["Interface/Garrison/GarrBuildingLeatherworking2AMap"]={
		["GarrBuilding_Leatherworking_2_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking2AMap
	["Interface/Garrison/GarrBuildingLeatherworking2HInfo"]={
		["GarrBuilding_Leatherworking_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking2HInfo
	["Interface/Garrison/GarrBuildingLeatherworking2HMap"]={
		["GarrBuilding_Leatherworking_2_H_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking2HMap
	["Interface/Garrison/GarrBuildingLeatherworking3AInfo"]={
		["GarrBuilding_Leatherworking_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking3AInfo
	["Interface/Garrison/GarrBuildingLeatherworking3AMap"]={
		["GarrBuilding_Leatherworking_3_A_Map"]={108, 74, 0.0078125, 0.851562, 0.0078125, 0.585938, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking3AMap
	["Interface/Garrison/GarrBuildingLeatherworking3HInfo"]={
		["GarrBuilding_Leatherworking_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking3HInfo
	["Interface/Garrison/GarrBuildingLeatherworking3HMap"]={
		["GarrBuilding_Leatherworking_3_H_Map"]={108, 87, 0.0078125, 0.851562, 0.0078125, 0.6875, false, false},
	}, -- Interface/Garrison/GarrBuildingLeatherworking3HMap
	["Interface/Garrison/GarrBuildingLumberMill1AInfo"]={
		["GarrBuilding_LumberMill_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill1AInfo
	["Interface/Garrison/GarrBuildingLumberMill1AMap"]={
		["GarrBuilding_LumberMill_1_A_Map"]={108, 70, 0.0078125, 0.851562, 0.0078125, 0.554688, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill1AMap
	["Interface/Garrison/GarrBuildingLumberMill1HInfo"]={
		["GarrBuilding_LumberMill_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill1HInfo
	["Interface/Garrison/GarrBuildingLumberMill1HMap"]={
		["GarrBuilding_LumberMill_1_H_Map"]={108, 73, 0.0078125, 0.851562, 0.0078125, 0.578125, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill1HMap
	["Interface/Garrison/GarrBuildingLumberMill2AInfo"]={
		["GarrBuilding_LumberMill_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill2AInfo
	["Interface/Garrison/GarrBuildingLumberMill2AMap"]={
		["GarrBuilding_LumberMill_2_A_Map"]={108, 79, 0.0078125, 0.851562, 0.0078125, 0.625, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill2AMap
	["Interface/Garrison/GarrBuildingLumberMill2HInfo"]={
		["GarrBuilding_LumberMill_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill2HInfo
	["Interface/Garrison/GarrBuildingLumberMill2HMap"]={
		["GarrBuilding_LumberMill_2_H_Map"]={108, 82, 0.0078125, 0.851562, 0.0078125, 0.648438, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill2HMap
	["Interface/Garrison/GarrBuildingLumberMill3AInfo"]={
		["GarrBuilding_LumberMill_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill3AInfo
	["Interface/Garrison/GarrBuildingLumberMill3AMap"]={
		["GarrBuilding_LumberMill_3_A_Map"]={108, 99, 0.0078125, 0.851562, 0.0078125, 0.78125, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill3AMap
	["Interface/Garrison/GarrBuildingLumberMill3HInfo"]={
		["GarrBuilding_LumberMill_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill3HInfo
	["Interface/Garrison/GarrBuildingLumberMill3HMap"]={
		["GarrBuilding_LumberMill_3_H_Map"]={108, 94, 0.0078125, 0.851562, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/GarrBuildingLumberMill3HMap
	["Interface/Garrison/GarrBuildingMageTower1AInfo"]={
		["GarrBuilding_MageTower_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower1AInfo
	["Interface/Garrison/GarrBuildingMageTower1AMap"]={
		["GarrBuilding_MageTower_1_A_Map"]={108, 82, 0.0078125, 0.851562, 0.0078125, 0.648438, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower1AMap
	["Interface/Garrison/GarrBuildingMageTower1HInfo"]={
		["GarrBuilding_MageTower_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower1HInfo
	["Interface/Garrison/GarrBuildingMageTower1HMap"]={
		["GarrBuilding_MageTower_1_H_Map"]={108, 90, 0.0078125, 0.851562, 0.0078125, 0.710938, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower1HMap
	["Interface/Garrison/GarrBuildingMageTower2AInfo"]={
		["GarrBuilding_MageTower_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower2AInfo
	["Interface/Garrison/GarrBuildingMageTower2AMap"]={
		["GarrBuilding_MageTower_2_A_Map"]={108, 113, 0.0078125, 0.851562, 0.0078125, 0.890625, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower2AMap
	["Interface/Garrison/GarrBuildingMageTower2HInfo"]={
		["GarrBuilding_MageTower_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower2HInfo
	["Interface/Garrison/GarrBuildingMageTower2HMap"]={
		["GarrBuilding_MageTower_2_H_Map"]={108, 111, 0.0078125, 0.851562, 0.0078125, 0.875, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower2HMap
	["Interface/Garrison/GarrBuildingMageTower3AInfo"]={
		["GarrBuilding_MageTower_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower3AInfo
	["Interface/Garrison/GarrBuildingMageTower3AMap"]={
		["GarrBuilding_MageTower_3_A_Map"]={108, 146, 0.0078125, 0.851562, 0.********, 0.574219, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower3AMap
	["Interface/Garrison/GarrBuildingMageTower3HInfo"]={
		["GarrBuilding_MageTower_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower3HInfo
	["Interface/Garrison/GarrBuildingMageTower3HMap"]={
		["GarrBuilding_MageTower_3_H_Map"]={108, 125, 0.0078125, 0.851562, 0.0078125, 0.984375, false, false},
	}, -- Interface/Garrison/GarrBuildingMageTower3HMap
	["Interface/Garrison/GarrBuildingMine1AInfo"]={
		["GarrBuilding_Mine_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMine1AInfo
	["Interface/Garrison/GarrBuildingMine1AMap"]={
		["GarrBuilding_Mine_1_A_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingMine1AMap
	["Interface/Garrison/GarrBuildingMine1HInfo"]={
		["GarrBuilding_Mine_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMine1HInfo
	["Interface/Garrison/GarrBuildingMine1HMap"]={
		["GarrBuilding_Mine_1_H_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingMine1HMap
	["Interface/Garrison/GarrBuildingMine2AInfo"]={
		["GarrBuilding_Mine_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMine2AInfo
	["Interface/Garrison/GarrBuildingMine2AMap"]={
		["GarrBuilding_Mine_2_A_Map"]={108, 77, 0.0078125, 0.851562, 0.0078125, 0.609375, false, false},
	}, -- Interface/Garrison/GarrBuildingMine2AMap
	["Interface/Garrison/GarrBuildingMine3AInfo"]={
		["GarrBuilding_Mine_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingMine3AInfo
	["Interface/Garrison/GarrBuildingMine3AMap"]={
		["GarrBuilding_Mine_3_A_Map"]={108, 77, 0.0078125, 0.851562, 0.0078125, 0.609375, false, false},
	}, -- Interface/Garrison/GarrBuildingMine3AMap
	["Interface/Garrison/GarrBuildingPetStable1AInfo"]={
		["GarrBuilding_PetStable_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingPetStable1AInfo
	["Interface/Garrison/GarrBuildingPetStable1AMap"]={
		["GarrBuilding_PetStable_1_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingPetStable1AMap
	["Interface/Garrison/GarrBuildingPetStable1HInfo"]={
		["GarrBuilding_PetStable_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingPetStable1HInfo
	["Interface/Garrison/GarrBuildingPetStable1HMap"]={
		["GarrBuilding_PetStable_1_H_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingPetStable1HMap
	["Interface/Garrison/GarrBuildingSalvageYard1AInfo"]={
		["GarrBuilding_SalvageYard_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSalvageYard1AInfo
	["Interface/Garrison/GarrBuildingSalvageYard1AMap"]={
		["GarrBuilding_SalvageYard_1_A_Map"]={108, 89, 0.0078125, 0.851562, 0.0078125, 0.703125, false, false},
	}, -- Interface/Garrison/GarrBuildingSalvageYard1AMap
	["Interface/Garrison/GarrBuildingSalvageYard1HInfo"]={
		["GarrBuilding_SalvageYard_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSalvageYard1HInfo
	["Interface/Garrison/GarrBuildingSalvageYard1HMap"]={
		["GarrBuilding_SalvageYard_1_H_Map"]={108, 86, 0.0078125, 0.851562, 0.0078125, 0.679688, false, false},
	}, -- Interface/Garrison/GarrBuildingSalvageYard1HMap
	["Interface/Garrison/GarrBuildingSparringArena1AInfo"]={
		["GarrBuilding_SparringArena_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena1AInfo
	["Interface/Garrison/GarrBuildingSparringArena1AMap"]={
		["GarrBuilding_SparringArena_1_A_Map"]={108, 66, 0.0078125, 0.851562, 0.0078125, 0.523438, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena1AMap
	["Interface/Garrison/GarrBuildingSparringArena1HInfo"]={
		["GarrBuilding_SparringArena_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena1HInfo
	["Interface/Garrison/GarrBuildingSparringArena1HMap"]={
		["GarrBuilding_SparringArena_1_H_Map"]={108, 67, 0.0078125, 0.851562, 0.0078125, 0.53125, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena1HMap
	["Interface/Garrison/GarrBuildingSparringArena2AInfo"]={
		["GarrBuilding_SparringArena_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena2AInfo
	["Interface/Garrison/GarrBuildingSparringArena2AMap"]={
		["GarrBuilding_SparringArena_2_A_Map"]={108, 81, 0.0078125, 0.851562, 0.0078125, 0.640625, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena2AMap
	["Interface/Garrison/GarrBuildingSparringArena2HInfo"]={
		["GarrBuilding_SparringArena_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena2HInfo
	["Interface/Garrison/GarrBuildingSparringArena2HMap"]={
		["GarrBuilding_SparringArena_2_H_Map"]={108, 76, 0.0078125, 0.851562, 0.0078125, 0.601562, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena2HMap
	["Interface/Garrison/GarrBuildingSparringArena3AInfo"]={
		["GarrBuilding_SparringArena_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena3AInfo
	["Interface/Garrison/GarrBuildingSparringArena3AMap"]={
		["GarrBuilding_SparringArena_3_A_Map"]={108, 77, 0.0078125, 0.851562, 0.0078125, 0.609375, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena3AMap
	["Interface/Garrison/GarrBuildingSparringArena3HInfo"]={
		["GarrBuilding_SparringArena_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena3HInfo
	["Interface/Garrison/GarrBuildingSparringArena3HMap"]={
		["GarrBuilding_SparringArena_3_H_Map"]={108, 70, 0.0078125, 0.851562, 0.0078125, 0.554688, false, false},
	}, -- Interface/Garrison/GarrBuildingSparringArena3HMap
	["Interface/Garrison/GarrBuildingStables1AInfo"]={
		["GarrBuilding_Stables_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStables1AInfo
	["Interface/Garrison/GarrBuildingStables1AMap"]={
		["GarrBuilding_Stables_1_A_Map"]={108, 80, 0.0078125, 0.851562, 0.0078125, 0.632812, false, false},
	}, -- Interface/Garrison/GarrBuildingStables1AMap
	["Interface/Garrison/GarrBuildingStables1HInfo"]={
		["GarrBuilding_Stables_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStables1HInfo
	["Interface/Garrison/GarrBuildingStables1HMap"]={
		["GarrBuilding_Stables_1_H_Map"]={108, 71, 0.0078125, 0.851562, 0.0078125, 0.5625, false, false},
	}, -- Interface/Garrison/GarrBuildingStables1HMap
	["Interface/Garrison/GarrBuildingStables2AInfo"]={
		["GarrBuilding_Stables_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStables2AInfo
	["Interface/Garrison/GarrBuildingStables2AMap"]={
		["GarrBuilding_Stables_2_A_Map"]={108, 71, 0.0078125, 0.851562, 0.0078125, 0.5625, false, false},
	}, -- Interface/Garrison/GarrBuildingStables2AMap
	["Interface/Garrison/GarrBuildingStables2HInfo"]={
		["GarrBuilding_Stables_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStables2HInfo
	["Interface/Garrison/GarrBuildingStables2HMap"]={
		["GarrBuilding_Stables_2_H_Map"]={108, 71, 0.0078125, 0.851562, 0.0078125, 0.5625, false, false},
	}, -- Interface/Garrison/GarrBuildingStables2HMap
	["Interface/Garrison/GarrBuildingStables3AInfo"]={
		["GarrBuilding_Stables_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStables3AInfo
	["Interface/Garrison/GarrBuildingStables3AMap"]={
		["GarrBuilding_Stables_3_A_Map"]={108, 72, 0.0078125, 0.851562, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/GarrBuildingStables3AMap
	["Interface/Garrison/GarrBuildingStables3HInfo"]={
		["GarrBuilding_Stables_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStables3HInfo
	["Interface/Garrison/GarrBuildingStables3HMap"]={
		["GarrBuilding_Stables_3_H_Map"]={108, 87, 0.0078125, 0.851562, 0.0078125, 0.6875, false, false},
	}, -- Interface/Garrison/GarrBuildingStables3HMap
	["Interface/Garrison/GarrBuildingStorehouse1AInfo"]={
		["GarrBuilding_Storehouse_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStorehouse1AInfo
	["Interface/Garrison/GarrBuildingStorehouse1AMap"]={
		["GarrBuilding_Storehouse_1_A_Map"]={108, 73, 0.0078125, 0.851562, 0.0078125, 0.578125, false, false},
	}, -- Interface/Garrison/GarrBuildingStorehouse1AMap
	["Interface/Garrison/GarrBuildingStorehouse1HInfo"]={
		["GarrBuilding_Storehouse_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingStorehouse1HInfo
	["Interface/Garrison/GarrBuildingStorehouse1HMap"]={
		["GarrBuilding_Storehouse_1_H_Map"]={108, 80, 0.0078125, 0.851562, 0.0078125, 0.632812, false, false},
	}, -- Interface/Garrison/GarrBuildingStorehouse1HMap
	["Interface/Garrison/GarrBuildingTailoring1AInfo"]={
		["GarrBuilding_Tailoring_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring1AInfo
	["Interface/Garrison/GarrBuildingTailoring1AMap"]={
		["GarrBuilding_Tailoring_1_A_Map"]={108, 73, 0.0078125, 0.851562, 0.0078125, 0.578125, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring1AMap
	["Interface/Garrison/GarrBuildingTailoring1HInfo"]={
		["GarrBuilding_Tailoring_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring1HInfo
	["Interface/Garrison/GarrBuildingTailoring1HMap"]={
		["GarrBuilding_Tailoring_1_H_Map"]={108, 86, 0.0078125, 0.851562, 0.0078125, 0.679688, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring1HMap
	["Interface/Garrison/GarrBuildingTailoring2AInfo"]={
		["GarrBuilding_Tailoring_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring2AInfo
	["Interface/Garrison/GarrBuildingTailoring2AMap"]={
		["GarrBuilding_Tailoring_2_A_Map"]={108, 75, 0.0078125, 0.851562, 0.0078125, 0.59375, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring2AMap
	["Interface/Garrison/GarrBuildingTailoring2HInfo"]={
		["GarrBuilding_Tailoring_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring2HInfo
	["Interface/Garrison/GarrBuildingTailoring2HMap"]={
		["GarrBuilding_Tailoring_2_H_Map"]={108, 83, 0.0078125, 0.851562, 0.0078125, 0.65625, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring2HMap
	["Interface/Garrison/GarrBuildingTailoring3AInfo"]={
		["GarrBuilding_Tailoring_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring3AInfo
	["Interface/Garrison/GarrBuildingTailoring3AMap"]={
		["GarrBuilding_Tailoring_3_A_Map"]={108, 76, 0.0078125, 0.851562, 0.0078125, 0.601562, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring3AMap
	["Interface/Garrison/GarrBuildingTailoring3HInfo"]={
		["GarrBuilding_Tailoring_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring3HInfo
	["Interface/Garrison/GarrBuildingTailoring3HMap"]={
		["GarrBuilding_Tailoring_3_H_Map"]={108, 84, 0.0078125, 0.851562, 0.0078125, 0.664062, false, false},
	}, -- Interface/Garrison/GarrBuildingTailoring3HMap
	["Interface/Garrison/GarrBuildingTownHall1AInfo"]={
		["GarrBuilding_TownHall_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall1AInfo
	["Interface/Garrison/GarrBuildingTownHall1AMap"]={
		["GarrBuilding_TownHall_1_A_Map"]={108, 101, 0.0078125, 0.851562, 0.0078125, 0.796875, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall1AMap
	["Interface/Garrison/GarrBuildingTownHall1HInfo"]={
		["GarrBuilding_TownHall_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall1HInfo
	["Interface/Garrison/GarrBuildingTownHall1HMap"]={
		["GarrBuilding_TownHall_1_H_Map"]={108, 102, 0.0078125, 0.851562, 0.0078125, 0.804688, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall1HMap
	["Interface/Garrison/GarrBuildingTownHall2AInfo"]={
		["GarrBuilding_TownHall_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall2AInfo
	["Interface/Garrison/GarrBuildingTownHall2AMap"]={
		["GarrBuilding_TownHall_2_A_Map"]={108, 110, 0.0078125, 0.851562, 0.0078125, 0.867188, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall2AMap
	["Interface/Garrison/GarrBuildingTownHall2HInfo"]={
		["GarrBuilding_TownHall_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall2HInfo
	["Interface/Garrison/GarrBuildingTownHall2HMap"]={
		["GarrBuilding_TownHall_2_H_Map"]={108, 107, 0.0078125, 0.851562, 0.0078125, 0.84375, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall2HMap
	["Interface/Garrison/GarrBuildingTownHall3AInfo"]={
		["GarrBuilding_TownHall_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall3AInfo
	["Interface/Garrison/GarrBuildingTownHall3AMap"]={
		["GarrBuilding_TownHall_3_A_Map"]={108, 141, 0.0078125, 0.851562, 0.********, 0.554688, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall3AMap
	["Interface/Garrison/GarrBuildingTownHall3HInfo"]={
		["GarrBuilding_TownHall_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall3HInfo
	["Interface/Garrison/GarrBuildingTownHall3HMap"]={
		["GarrBuilding_TownHall_3_H_Map"]={108, 111, 0.0078125, 0.851562, 0.0078125, 0.875, false, false},
	}, -- Interface/Garrison/GarrBuildingTownHall3HMap
	["Interface/Garrison/GarrBuildingTradingPost1AInfo"]={
		["GarrBuilding_TradingPost_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost1AInfo
	["Interface/Garrison/GarrBuildingTradingPost1AMap"]={
		["GarrBuilding_TradingPost_1_A_Map"]={108, 77, 0.0078125, 0.851562, 0.0078125, 0.609375, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost1AMap
	["Interface/Garrison/GarrBuildingTradingPost1HInfo"]={
		["GarrBuilding_TradingPost_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost1HInfo
	["Interface/Garrison/GarrBuildingTradingPost1HMap"]={
		["GarrBuilding_TradingPost_1_H_Map"]={108, 65, 0.0078125, 0.851562, 0.0078125, 0.515625, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost1HMap
	["Interface/Garrison/GarrBuildingTradingPost2AInfo"]={
		["GarrBuilding_TradingPost_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost2AInfo
	["Interface/Garrison/GarrBuildingTradingPost2AMap"]={
		["GarrBuilding_TradingPost_2_A_Map"]={108, 94, 0.0078125, 0.851562, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost2AMap
	["Interface/Garrison/GarrBuildingTradingPost2HInfo"]={
		["GarrBuilding_TradingPost_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost2HInfo
	["Interface/Garrison/GarrBuildingTradingPost2HMap"]={
		["GarrBuilding_TradingPost_2_H_Map"]={108, 95, 0.0078125, 0.851562, 0.0078125, 0.75, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost2HMap
	["Interface/Garrison/GarrBuildingTradingPost3AInfo"]={
		["GarrBuilding_TradingPost_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost3AInfo
	["Interface/Garrison/GarrBuildingTradingPost3AMap"]={
		["GarrBuilding_TradingPost_3_A_Map"]={108, 85, 0.0078125, 0.851562, 0.0078125, 0.671875, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost3AMap
	["Interface/Garrison/GarrBuildingTradingPost3HInfo"]={
		["GarrBuilding_TradingPost_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost3HInfo
	["Interface/Garrison/GarrBuildingTradingPost3HMap"]={
		["GarrBuilding_TradingPost_3_H_Map"]={108, 93, 0.0078125, 0.851562, 0.0078125, 0.734375, false, false},
	}, -- Interface/Garrison/GarrBuildingTradingPost3HMap
	["Interface/Garrison/GarrBuildingWorkshop1AInfo"]={
		["GarrBuilding_Workshop_1_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop1AInfo
	["Interface/Garrison/GarrBuildingWorkshop1AMap"]={
		["GarrBuilding_Workshop_1_A_Map"]={108, 78, 0.0078125, 0.851562, 0.0078125, 0.617188, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop1AMap
	["Interface/Garrison/GarrBuildingWorkshop1HInfo"]={
		["GarrBuilding_Workshop_1_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop1HInfo
	["Interface/Garrison/GarrBuildingWorkshop1HMap"]={
		["GarrBuilding_Workshop_1_H_Map"]={108, 101, 0.0078125, 0.851562, 0.0078125, 0.796875, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop1HMap
	["Interface/Garrison/GarrBuildingWorkshop2AInfo"]={
		["GarrBuilding_Workshop_2_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop2AInfo
	["Interface/Garrison/GarrBuildingWorkshop2AMap"]={
		["GarrBuilding_Workshop_2_A_Map"]={108, 87, 0.0078125, 0.851562, 0.0078125, 0.6875, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop2AMap
	["Interface/Garrison/GarrBuildingWorkshop2HInfo"]={
		["GarrBuilding_Workshop_2_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop2HInfo
	["Interface/Garrison/GarrBuildingWorkshop2HMap"]={
		["GarrBuilding_Workshop_2_H_Map"]={108, 96, 0.0078125, 0.851562, 0.0078125, 0.757812, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop2HMap
	["Interface/Garrison/GarrBuildingWorkshop3AInfo"]={
		["GarrBuilding_Workshop_3_A_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop3AInfo
	["Interface/Garrison/GarrBuildingWorkshop3AMap"]={
		["GarrBuilding_Workshop_3_A_Map"]={108, 96, 0.0078125, 0.851562, 0.0078125, 0.757812, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop3AMap
	["Interface/Garrison/GarrBuildingWorkshop3HInfo"]={
		["GarrBuilding_Workshop_3_H_Info"]={250, 162, 0.********, 0.980469, 0.********, 0.636719, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop3HInfo
	["Interface/Garrison/GarrBuildingWorkshop3HMap"]={
		["GarrBuilding_Workshop_3_H_Map"]={108, 97, 0.0078125, 0.851562, 0.0078125, 0.765625, false, false},
	}, -- Interface/Garrison/GarrBuildingWorkshop3HMap
	["Interface/Garrison/GarrisonBuildings1A"]={
		["GarrBuilding_Default_Info"]={250, 162, 0.********, 0.490234, 0.322266, 0.638672, false, false},
		["GarrBuilding_Default_Map"]={108, 80, 0.525391, 0.736328, 0.********, 0.158203, false, false},
		["GarrBuilding_EmptyPlot_1_A_Info"]={266, 162, 0.********, 0.521484, 0.********, 0.318359, false, false},
	}, -- Interface/Garrison/GarrisonBuildings1A
	["Interface/Garrison/GarrisonBuildingUI"]={
		["Garr_BuildingIconRing_"]={36, 37, 0.0908203, 0.125977, 0.681641, 0.753906, false, false},
		["Garr_BuildingPlacementExplosion"]={97, 63, 0.*********, 0.0957031, 0.865234, 0.988281, false, false},
		["Garr_CostBar"]={175, 44, 0.333008, 0.503906, 0.246094, 0.332031, false, false},
		["Garr_FollowerPortrait_Bg"]={58, 58, 0.342773, 0.399414, 0.884766, 0.998047, false, false},
		["Garr_FollowerPortrait_Ring"]={58, 58, 0.464844, 0.521484, 0.453125, 0.566406, false, false},
		["Garr_FollowerPortrait_TimerBG"]={45, 45, 0.938477, 0.982422, 0.********, 0.0898438, false, false},
		["Garr_FollowerPortrait_TimerFill"]={45, 45, 0.938477, 0.982422, 0.09375, 0.181641, false, false},
		["Garr_LevelBadge_1"]={58, 49, 0.518555, 0.575195, 0.337891, 0.433594, false, false},
		["Garr_LevelBadge_2"]={58, 49, 0.577148, 0.633789, 0.337891, 0.433594, false, false},
		["Garr_LevelBadge_3"]={58, 49, 0.635742, 0.692383, 0.337891, 0.433594, false, false},
		["Garr_LevelUpgradeArrow"]={33, 39, 0.0908203, 0.123047, 0.757812, 0.833984, false, false},
		["Garr_LevelUpgradeLocked"]={33, 39, 0.794922, 0.827148, 0.246094, 0.322266, false, false},
		["Garr_ListButton-Highlight"]={208, 45, 0.733398, 0.936523, 0.09375, 0.181641, false, false},
		["Garr_ListButton-Selection"]={208, 45, 0.12793, 0.331055, 0.246094, 0.333984, false, false},
		["Garr_ListButton"]={208, 45, 0.733398, 0.936523, 0.********, 0.0898438, false, false},
		["Garr_ListTab-Highlight"]={77, 36, 0.59375, 0.668945, 0.246094, 0.316406, false, false},
		["Garr_ListTab-Select"]={91, 50, 0.375, 0.463867, 0.337891, 0.435547, false, false},
		["Garr_ListTab"]={91, 50, 0.28418, 0.373047, 0.337891, 0.435547, false, false},
		["Garr_MaterialIcon"]={12, 11, 0.591797, 0.603516, 0.123047, 0.144531, false, false},
		["Garr_MonumentLocked"]={58, 58, 0.464844, 0.521484, 0.570312, 0.683594, false, false},
		["Garr_MonumentOpen"]={58, 58, 0.464844, 0.521484, 0.6875, 0.800781, false, false},
		["Garr_PlanIcon-List"]={38, 38, 0.552734, 0.589844, 0.123047, 0.197266, false, false},
		["Garr_PlansRequiredIcon"]={90, 92, 0.*********, 0.0888672, 0.681641, 0.861328, false, false},
		["Garr_Plot_Glow_3"]={108, 70, 0.12793, 0.233398, 0.734375, 0.871094, false, false},
		["Garr_Plot_Glow_2"]={108, 70, 0.12793, 0.233398, 0.59375, 0.730469, false, false},
		["Garr_Plot_Glow_1"]={108, 70, 0.12793, 0.233398, 0.453125, 0.589844, false, false},
		["Garr_Plot_Glow_4"]={108, 70, 0.235352, 0.34082, 0.453125, 0.589844, false, false},
		["Garr_Specialization_FrameLeft"]={52, 57, 0.46582, 0.516602, 0.337891, 0.449219, false, false},
		["Garr_Specialization_FrameMid"]={43, 57, 0.897461, 0.939453, 0.337891, 0.449219, false, false},
		["Garr_Specialization_IconBorder"]={45, 45, 0.749023, 0.792969, 0.246094, 0.333984, false, false},
		["Garr_Specialization_IconSelected"]={49, 49, 0.941406, 0.989258, 0.337891, 0.433594, false, false},
		["Garr_TownHallBanner_Left"]={36, 33, 0.829102, 0.864258, 0.246094, 0.310547, false, false},
		["Garr_TownHallBanner_LevelFiligree"]={88, 38, 0.505859, 0.591797, 0.246094, 0.320312, false, false},
		["Garr_TownHallBanner_Mid"]={78, 34, 0.670898, 0.74707, 0.246094, 0.3125, false, false},
		["Garr_TownHallBanner_Right"]={36, 33, 0.866211, 0.901367, 0.246094, 0.310547, false, false},
		["Garr_TreasureIcon"]={66, 63, 0.342773, 0.407227, 0.597656, 0.720703, false, false},
		["Garr_XPBar_Left"]={32, 21, 0.620117, 0.651367, 0.179688, 0.220703, false, false},
		["Garr_XPBar_Nub"]={11, 11, 0.605469, 0.616211, 0.123047, 0.144531, false, false},
		["Garr_BuildIcon"]={46, 46, 0.235352, 0.280273, 0.904297, 0.994141, false, false},
		["Garr_BuildingConfirmation"]={271, 123, 0.*********, 0.265625, 0.********, 0.242188, false, false},
		["Garr_BuildingIconTimerBG"]={34, 34, 0.90332, 0.936523, 0.246094, 0.3125, false, false},
		["Garr_BuildingIconTimerFill"]={34, 34, 0.938477, 0.97168, 0.246094, 0.3125, false, false},
		["Garr_BuildingTimerBG"]={50, 50, 0.694336, 0.743164, 0.337891, 0.435547, false, false},
		["Garr_BuildingTimerFill"]={50, 50, 0.745117, 0.793945, 0.337891, 0.435547, false, false},
		["Garr_BuildingTimerGlow"]={72, 72, 0.235352, 0.305664, 0.759766, 0.900391, false, false},
		["Garr_BuildingUpgradeExplosion"]={97, 63, 0.12793, 0.222656, 0.875, 0.998047, false, false},
		["Garr_LevelBadgeGlow"]={96, 91, 0.*********, 0.0947266, 0.5, 0.677734, false, false},
		["Garr_NotificationGlow"]={359, 60, 0.267578, 0.618164, 0.********, 0.119141, false, false},
		["Garr_UpgradeBanner"]={114, 89, 0.620117, 0.731445, 0.********, 0.175781, false, false},
		["Garr_UpgradeIcon"]={46, 46, 0.415039, 0.459961, 0.613281, 0.703125, false, false},
		["Garr_UpgradeIconTimerBG"]={34, 34, 0.303711, 0.336914, 0.59375, 0.660156, false, false},
		["Garr_UpgradeIconTimerFill"]={34, 34, 0.303711, 0.336914, 0.664062, 0.730469, false, false},
		["Garr_UpgradeTimerBG"]={50, 50, 0.795898, 0.844727, 0.337891, 0.435547, false, false},
		["Garr_UpgradeTimerFill"]={50, 50, 0.84668, 0.895508, 0.337891, 0.435547, false, false},
		["Garr_UpgradeTimerGlow"]={72, 72, 0.342773, 0.413086, 0.453125, 0.59375, false, false},
		["Garr_BuildingShadowOverlay"]={128, 128, 0.*********, 0.125977, 0.246094, 0.496094, false, false},
		["Garr_BuildingInfoShadow"]={158, 57, 0.12793, 0.282227, 0.337891, 0.449219, false, false},
		["Garr_Building-AddFollowerPlus"]={33, 33, 0.307617, 0.339844, 0.759766, 0.824219, false, false},
		["Garr_SwapIcon"]={46, 46, 0.282227, 0.327148, 0.904297, 0.994141, false, false},
		["Garr_BuildFX-Glow"]={49, 80, 0.342773, 0.390625, 0.724609, 0.880859, false, false},
		["Garr_BuildFX-Lines"]={27, 84, 0.0966797, 0.123047, 0.5, 0.664062, false, false},
		["Garr_LockedBuilding"]={68, 83, 0.235352, 0.301758, 0.59375, 0.755859, false, false},
		["Garr_UpgradeFX-Glow"]={49, 80, 0.415039, 0.462891, 0.453125, 0.609375, false, false},
		["Garr_Building_MaterialFrame"]={290, 43, 0.267578, 0.550781, 0.123047, 0.207031, false, false},
	}, -- Interface/Garrison/GarrisonBuildingUI
	["Interface/Garrison/GarrisonCacheToast"]={
		["CacheToast-Glow"]={286, 109, 0.********, 0.560547, 0.********, 0.429688, false, false},
		["CacheToast"]={276, 96, 0.********, 0.541016, 0.4375, 0.8125, false, false},
	}, -- Interface/Garrison/GarrisonCacheToast
	["Interface/Garrison/GarrisonCurrencyIcons"]={
		["GarrMission_CurrencyIcon-Material"]={57, 57, 0.0078125, 0.453125, 0.0078125, 0.453125, false, false},
		["GarrMission_CurrencyIcon-Salvage"]={57, 57, 0.46875, 0.914062, 0.0078125, 0.453125, false, false},
		["GarrMission_CurrencyIcon-Xp"]={57, 57, 0.0078125, 0.453125, 0.46875, 0.914062, false, false},
		["ShipMission_CurrencyIcon-Oil"]={57, 57, 0.46875, 0.914062, 0.46875, 0.914062, false, false},
	}, -- Interface/Garrison/GarrisonCurrencyIcons
	["Interface/Garrison/GarrisonFollowerClassIcons"]={
		["GarrMission_ClassIcon-Hunter"]={55, 49, 0.449219, 0.664062, 0.********, 0.195312, false, false},
		["GarrMission_ClassIcon-Warrior"]={55, 49, 0.671875, 0.886719, 0.203125, 0.394531, false, false},
		["GarrMission_ClassIcon-Priest"]={55, 49, 0.********, 0.21875, 0.601562, 0.792969, false, false},
		["GarrMission_ClassIcon-Monk"]={55, 49, 0.********, 0.21875, 0.203125, 0.394531, false, false},
		["GarrMission_ClassIcon-Rogue"]={55, 49, 0.********, 0.21875, 0.800781, 0.992188, false, false},
		["GarrMission_ClassIcon-Mage"]={55, 49, 0.671875, 0.886719, 0.********, 0.195312, false, false},
		["GarrMission_ClassIcon-Warlock"]={55, 49, 0.449219, 0.664062, 0.203125, 0.394531, false, false},
		["GarrMission_ClassIcon-Shaman"]={55, 49, 0.226562, 0.441406, 0.203125, 0.394531, false, false},
		["GarrMission_ClassIcon-Druid"]={55, 49, 0.226562, 0.441406, 0.********, 0.195312, false, false},
		["GarrMission_ClassIcon-Paladin"]={55, 49, 0.********, 0.21875, 0.402344, 0.59375, false, false},
		["GarrMission_ClassIcon-DeathKnight"]={55, 49, 0.********, 0.21875, 0.********, 0.195312, false, false},
	}, -- Interface/Garrison/GarrisonFollowerClassIcons
	["Interface/Garrison/GarrisonFollowerSpecIcons"]={
		["GarrMission_ClassIcon-DeathKnight-Blood"]={55, 49, 0.335938, 0.443359, 0.300781, 0.396484, false, false},
		["GarrMission_ClassIcon-DeathKnight-Frost"]={55, 49, 0.669922, 0.777344, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-DeathKnight-Unholy"]={55, 49, 0.224609, 0.332031, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-DemonHunter-Havoc"]={55, 49, 0.113281, 0.220703, 0.400391, 0.496094, false, false},
		["GarrMission_ClassIcon-DemonHunter-Vengeance"]={55, 49, 0.113281, 0.220703, 0.5, 0.595703, false, false},
		["GarrMission_ClassIcon-Druid-Balance"]={55, 49, 0.558594, 0.666016, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Druid-Feral"]={55, 49, 0.********, 0.109375, 0.699219, 0.794922, false, false},
		["GarrMission_ClassIcon-Druid-Guardian"]={55, 49, 0.447266, 0.554688, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-Druid-Restoration"]={55, 49, 0.78125, 0.888672, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-Hunter-BeastMastery"]={55, 49, 0.224609, 0.332031, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Hunter-Marksmanship"]={55, 49, 0.78125, 0.888672, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Hunter-Survival"]={55, 49, 0.669922, 0.777344, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Mage-Arcane"]={55, 49, 0.********, 0.109375, 0.400391, 0.496094, false, false},
		["GarrMission_ClassIcon-Mage-Fire"]={55, 49, 0.558594, 0.666016, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Mage-Frost"]={55, 49, 0.********, 0.109375, 0.599609, 0.695312, false, false},
		["GarrMission_ClassIcon-Monk-Brewmaster"]={55, 49, 0.224609, 0.332031, 0.300781, 0.396484, false, false},
		["GarrMission_ClassIcon-Monk-Mistweaver"]={55, 49, 0.447266, 0.554688, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Monk-Windwalker"]={55, 49, 0.669922, 0.777344, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-Paladin-Holy"]={55, 49, 0.335938, 0.443359, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-Paladin-Protection"]={55, 49, 0.113281, 0.220703, 0.599609, 0.695312, false, false},
		["GarrMission_ClassIcon-Paladin-Retribution"]={55, 49, 0.113281, 0.220703, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Priest-Discipline"]={55, 49, 0.113281, 0.220703, 0.300781, 0.396484, false, false},
		["GarrMission_ClassIcon-Priest-Holy"]={55, 49, 0.113281, 0.220703, 0.798828, 0.894531, false, false},
		["GarrMission_ClassIcon-Priest-Shadow"]={55, 49, 0.224609, 0.332031, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Rogue-Assassination"]={55, 49, 0.447266, 0.554688, 0.300781, 0.396484, false, false},
		["GarrMission_ClassIcon-Rogue-Outlaw"]={55, 49, 0.********, 0.109375, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Rogue-Subtlety"]={55, 49, 0.335938, 0.443359, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Shaman-Elemental"]={55, 49, 0.558594, 0.666016, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-Shaman-Enhancement"]={55, 49, 0.********, 0.109375, 0.798828, 0.894531, false, false},
		["GarrMission_ClassIcon-Shaman-Restoration"]={55, 49, 0.********, 0.109375, 0.300781, 0.396484, false, false},
		["GarrMission_ClassIcon-Warlock-Affliction"]={55, 49, 0.335938, 0.443359, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Warlock-Demonology"]={55, 49, 0.447266, 0.554688, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Warlock-Destruction"]={55, 49, 0.113281, 0.220703, 0.699219, 0.794922, false, false},
		["GarrMission_ClassIcon-Warrior-Arms"]={55, 49, 0.113281, 0.220703, 0.898438, 0.994141, false, false},
		["GarrMission_ClassIcon-Warrior-Fury"]={55, 49, 0.78125, 0.888672, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-Warrior-Protection"]={55, 49, 0.********, 0.109375, 0.5, 0.595703, false, false},
		["GarrMission_ClassIcon-DemonHunter-Outcast"]={55, 49, 0.********, 0.109375, 0.********, 0.0976562, false, false},
		["GarrMission_ClassIcon-Karazhan"]={55, 49, 0.********, 0.109375, 0.201172, 0.296875, false, false},
		["GarrMission_ClassIcon-Voidscarred"]={55, 49, 0.113281, 0.220703, 0.101562, 0.197266, false, false},
		["GarrMission_ClassIcon-ArgussianReach"]={55, 49, 0.********, 0.109375, 0.898438, 0.994141, false, false},
		["GarrMission_ClassIcon-ArmyoftheLight"]={55, 49, 0.113281, 0.220703, 0.201172, 0.296875, false, false},
	}, -- Interface/Garrison/GarrisonFollowerSpecIcons
	["Interface/Garrison/GarrisonLandingPage"]={
		["GarLanding-Bottom"]={412, 86, 0.0263672, 0.428711, 0.*********, 0.0849609, false, false},
		["GarLanding-Left"]={93, 225, 0.0263672, 0.117188, 0.144531, 0.364258, false, false},
		["GarLanding-Right"]={94, 225, 0.119141, 0.210938, 0.259766, 0.479492, false, false},
		["GarrLanding-InvasionBadge-Glow"]={280, 67, 0.119141, 0.392578, 0.192383, 0.257812, false, false},
		["GarrLanding-InvasionBadge"]={280, 67, 0.720703, 0.994141, 0.*********, 0.0664062, false, false},
		["GarrLanding-MissionListBG"]={478, 64, 0.507812, 0.974609, 0.602539, 0.665039, false, false},
		["GarrLanding-Top"]={413, 91, 0.507812, 0.911133, 0.307617, 0.396484, false, false},
		["GarrLanding-TradeskillTimer-BG"]={64, 64, 0.0263672, 0.0888672, 0.581055, 0.643555, false, false},
		["GarrLanding_Watermark-Tradeskill"]={279, 277, 0.713867, 0.986328, 0.712891, 0.983398, false, false},
		["GarrLanding-FollowerScrollFrame"]={300, 384, 0.212891, 0.505859, 0.307617, 0.682617, false, false},
		["GarrLanding-lowerleft"]={209, 158, 0.713867, 0.917969, 0.446289, 0.600586, false, false},
		["GarrLanding-lowerright"]={209, 158, 0.507812, 0.711914, 0.446289, 0.600586, false, false},
		["GarrLanding-upperleft"]={209, 158, 0.507812, 0.711914, 0.712891, 0.867188, false, false},
		["GarrLanding-upperright"]={208, 158, 0.212891, 0.416016, 0.783203, 0.9375, false, false},
		["GarrLanding-HeaderBar"]={772, 57, 0.0263672, 0.780273, 0.0869141, 0.142578, false, false},
		["GarrLanding-Mission-Complete"]={400, 47, 0.119141, 0.509766, 0.144531, 0.19043, false, false},
		["GarrLanding-Mission-InProgress"]={400, 47, 0.394531, 0.785156, 0.192383, 0.238281, false, false},
		["GarrLanding-Building-Complete"]={400, 47, 0.212891, 0.603516, 0.259766, 0.305664, false, false},
		["GarrLanding-Building-InProgress"]={400, 47, 0.511719, 0.902344, 0.144531, 0.19043, false, false},
		["GarrLanding-MinimapIcon-Alliance-Down"]={53, 53, 0.620117, 0.671875, 0.*********, 0.0527344, false, false},
		["GarrLanding-MinimapIcon-Alliance-Up"]={53, 53, 0.0263672, 0.078125, 0.419922, 0.47168, false, false},
		["GarrLanding-MinimapIcon-Horde-Up"]={53, 53, 0.0263672, 0.078125, 0.366211, 0.417969, false, false},
		["GarrLanding-MinimapIcon-Horde-Down"]={53, 53, 0.501953, 0.553711, 0.*********, 0.0527344, false, false},
		["GarrLanding-CircleGlow"]={46, 46, 0.673828, 0.71875, 0.*********, 0.0458984, false, false},
		["GarrLanding-MinimapAlertBG"]={174, 46, 0.782227, 0.952148, 0.0869141, 0.131836, false, false},
		["GarrLanding-SideToast-Glow"]={99, 99, 0.212891, 0.30957, 0.68457, 0.78125, false, false},
		["GarrLanding-TopTabHighlight"]={191, 24, 0.787109, 0.973633, 0.192383, 0.21582, false, false},
		["GarrLanding-TopTabSelected"]={205, 36, 0.212891, 0.413086, 0.939453, 0.974609, false, false},
		["GarrLanding-TopTabUnselected"]={205, 30, 0.787109, 0.987305, 0.217773, 0.24707, false, false},
		["GarrLanding_RewardsShadow"]={34, 34, 0.673828, 0.707031, 0.0478516, 0.0810547, false, false},
		["GarrLanding_RewardsListBG"]={132, 45, 0.507812, 0.636719, 0.666992, 0.710938, false, false},
		["GarrLandingList-CostBG"]={120, 18, 0.394531, 0.511719, 0.240234, 0.257812, false, false},
		["GarrLanding-TradeskillTimer-ParchmentBG"]={64, 64, 0.555664, 0.618164, 0.*********, 0.0634766, false, false},
		["GarrLanding-ShipmentCompleteGlow"]={96, 96, 0.311523, 0.405273, 0.68457, 0.77832, false, false},
		["GarrLanding-Tab-Bottom-Selected"]={71, 42, 0.0263672, 0.0957031, 0.473633, 0.514648, false, false},
		["GarrLanding-Tab-Bottom-Unselected"]={71, 42, 0.430664, 0.5, 0.0439453, 0.0849609, false, false},
		["GarrLanding-Tab-Bottom-Highlight"]={71, 42, 0.430664, 0.5, 0.*********, 0.0419922, false, false},
		["GarrLanding-Tab-Bottom-Selected-Right"]={24, 42, 0.*********, 0.0244141, 0.0869141, 0.12793, false, false},
		["GarrLanding-Tab-Bottom-Unselected-Left"]={24, 42, 0.*********, 0.0244141, 0.21582, 0.256836, false, false},
		["GarrLanding-Tab-Bottom-Selected-Left"]={24, 42, 0.*********, 0.0244141, 0.172852, 0.213867, false, false},
		["GarrLanding-Tab-Bottom-Highlight-Right"]={24, 42, 0.*********, 0.0244141, 0.0439453, 0.0849609, false, false},
		["GarrLanding-Tab-Bottom-Highlight-Left"]={24, 42, 0.*********, 0.0244141, 0.*********, 0.0419922, false, false},
		["GarrLanding-Tab-Bottom-Unselected-Right"]={24, 42, 0.*********, 0.0244141, 0.129883, 0.170898, false, false},
		["GarrLanding-TradeskillTimer-Border"]={64, 64, 0.0263672, 0.0888672, 0.516602, 0.579102, false, false},
		["GarrLanding-ShipMission-Complete"]={400, 47, 0.605469, 0.996094, 0.259766, 0.305664, false, false},
		["GarrLanding-ShipMission-InProgress"]={400, 47, 0.507812, 0.898438, 0.398438, 0.444336, false, false},
	}, -- Interface/Garrison/GarrisonLandingPage
	["Interface/Garrison/GarrisonLandingPageMiddleTile"]={
		["GarrLanding-MiddleTile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/GarrisonLandingPageMiddleTile
	["Interface/Garrison/GarrisonLootFX"]={
		["BonusChest-KeyholeBurst"]={118, 128, 0.265625, 0.496094, 0.265625, 0.515625, false, false},
		["BonusChest-KeyholeGlow"]={45, 57, 0.265625, 0.353516, 0.886719, 0.998047, false, false},
		["BonusChest-Lock"]={82, 104, 0.265625, 0.425781, 0.519531, 0.722656, false, false},
		["BonusChest-CircleGlow"]={99, 98, 0.********, 0.195312, 0.783203, 0.974609, false, false},
		["BonusChest-ItemBorder-Uncommon"]={66, 66, 0.791016, 0.919922, 0.125, 0.253906, false, false},
		["BonusChest-Burst-Uncommon"]={156, 133, 0.********, 0.306641, 0.********, 0.261719, false, false},
		["BonusChest-Smoke-Uncommon"]={80, 80, 0.265625, 0.421875, 0.726562, 0.882812, false, false},
		["BonusChest-OrangeSmoke-Wide"]={228, 64, 0.5, 0.945312, 0.265625, 0.390625, false, false},
		["BonusChest-OrangeGlow-Wide"]={244, 64, 0.310547, 0.787109, 0.125, 0.25, false, false},
		["GarrisonFX_BlueFlare"]={128, 128, 0.********, 0.251953, 0.529297, 0.779297, false, false},
		["GarrisonFX-StarBurst"]={133, 133, 0.********, 0.261719, 0.265625, 0.525391, false, false},
		["BonusChest-BonusTextGlow"]={311, 61, 0.310547, 0.917969, 0.********, 0.121094, false, false},
	}, -- Interface/Garrison/GarrisonLootFX
	["Interface/Garrison/GarrisonMapFrostfire1H"]={
		["Garr_Map_Frostfire1_H"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapFrostfire1H
	["Interface/Garrison/GarrisonMapFrostfire2H"]={
		["Garr_Map_Frostfire2_H"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapFrostfire2H
	["Interface/Garrison/GarrisonMapFrostfire3H"]={
		["Garr_Map_Frostfire3_H"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapFrostfire3H
	["Interface/Garrison/GarrisonMapFrostfire4H"]={
		["Garr_Map_Frostfire4_H"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapFrostfire4H
	["Interface/Garrison/GarrisonMapFrostfirePlots"]={
		["Garr_Plot_Frostfire_H_3"]={108, 70, 0.********, 0.425781, 0.********, 0.277344, false, false},
		["Garr_Plot_Frostfire_H_2"]={108, 70, 0.433594, 0.855469, 0.********, 0.277344, false, false},
		["Garr_Plot_Frostfire_H_1"]={108, 70, 0.********, 0.425781, 0.285156, 0.558594, false, false},
		["Garr_Plot_Frostfire_H_4"]={108, 70, 0.433594, 0.855469, 0.285156, 0.558594, false, false},
	}, -- Interface/Garrison/GarrisonMapFrostfirePlots
	["Interface/Garrison/GarrisonMapShadowmoon1A"]={
		["Garr_Map_Shadowmoon1_A"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapShadowmoon1A
	["Interface/Garrison/GarrisonMapShadowmoon2A"]={
		["Garr_Map_Shadowmoon2_A"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapShadowmoon2A
	["Interface/Garrison/GarrisonMapShadowmoon3A"]={
		["Garr_Map_Shadowmoon3_A"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapShadowmoon3A
	["Interface/Garrison/GarrisonMapShadowmoon4A"]={
		["Garr_Map_Shadowmoon4_A"]={672, 468, 0.*********, 0.657227, 0.********, 0.916016, false, false},
	}, -- Interface/Garrison/GarrisonMapShadowmoon4A
	["Interface/Garrison/GarrisonMapShadowmoonPlots"]={
		["Garr_Plot_Shadowmoon_A_4"]={108, 70, 0.433594, 0.855469, 0.285156, 0.558594, false, false},
		["Garr_Plot_Shadowmoon_A_1"]={108, 70, 0.********, 0.425781, 0.********, 0.277344, false, false},
		["Garr_Plot_Shadowmoon_A_2"]={108, 70, 0.433594, 0.855469, 0.********, 0.277344, false, false},
		["Garr_Plot_Shadowmoon_A_3"]={108, 70, 0.********, 0.425781, 0.285156, 0.558594, false, false},
	}, -- Interface/Garrison/GarrisonMapShadowmoonPlots
	["Interface/Garrison/GarrisonMissionLocationArgus"]={
		["_GarrMissionLocation-Argus-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationArgus
	["Interface/Garrison/GarrisonMissionLocationAzsuna"]={
		["_GarrMissionLocation-Azsuna-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationAzsuna
	["Interface/Garrison/GarrisonMissionLocationBlackrockMountain"]={
		["_GarrMissionLocation-BlackrockMountain-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-BlackrockMountain-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-BlackrockMountain-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationBlackrockMountain
	["Interface/Garrison/GarrisonMissionLocationDalaran"]={
		["_GarrMissionLocation-Dalaran-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationDalaran
	["Interface/Garrison/GarrisonMissionLocationFrostfireRidge"]={
		["_GarrMissionLocation-FrostfireRidge-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-FrostfireRidge-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-FrostfireRidge-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationFrostfireRidge
	["Interface/Garrison/GarrisonMissionLocationFrostfireSea"]={
		["_GarrMissionLocation-FrostfireSea-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-FrostfireSea-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-FrostfireSea-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationFrostfireSea
	["Interface/Garrison/GarrisonMissionLocationGorgrond"]={
		["_GarrMissionLocation-Gorgrond-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-Gorgrond-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-Gorgrond-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationGorgrond
	["Interface/Garrison/GarrisonMissionLocationHighmountain"]={
		["_GarrMissionLocation-Highmountain-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationHighmountain
	["Interface/Garrison/GarrisonMissionLocationLegion"]={
		["_GarrMissionLocation-Legion-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationLegion
	["Interface/Garrison/GarrisonMissionLocationLegionListBgs"]={
		["GarrMissionLocation-Azsuna-List"]={792, 78, 0.*********, 0.774414, 0.*********, 0.0771484, false, false},
		["GarrMissionLocation-Dalaran-List"]={792, 78, 0.*********, 0.774414, 0.0791016, 0.155273, false, false},
		["GarrMissionLocation-Highmountain-List"]={792, 78, 0.*********, 0.774414, 0.157227, 0.233398, false, false},
		["GarrMissionLocation-Legion-List"]={792, 78, 0.*********, 0.774414, 0.235352, 0.311523, false, false},
		["GarrMissionLocation-Stormheim-List"]={792, 78, 0.*********, 0.774414, 0.313477, 0.389648, false, false},
		["GarrMissionLocation-Suramar-List"]={792, 78, 0.*********, 0.774414, 0.391602, 0.467773, false, false},
		["GarrMissionLocation-Valsharah-List"]={792, 78, 0.*********, 0.774414, 0.469727, 0.545898, false, false},
		["GarrMissionLocation-Argus-List"]={792, 78, 0.*********, 0.774414, 0.547852, 0.624023, false, false},
	}, -- Interface/Garrison/GarrisonMissionLocationLegionListBgs
	["Interface/Garrison/GarrisonMissionLocationListBgs"]={
		["GarrMissionLocation-FrostfireRidge-List"]={792, 78, 0.*********, 0.774414, 0.0791016, 0.155273, false, false},
		["GarrMissionLocation-Gorgrond-List"]={792, 78, 0.*********, 0.774414, 0.157227, 0.233398, false, false},
		["GarrMissionLocation-Nagrand-List"]={792, 78, 0.*********, 0.774414, 0.235352, 0.311523, false, false},
		["GarrMissionLocation-ShadowmoonValley-List"]={792, 78, 0.*********, 0.774414, 0.313477, 0.389648, false, false},
		["GarrMissionLocation-SpiresofArak-List"]={792, 78, 0.*********, 0.774414, 0.391602, 0.467773, false, false},
		["GarrMissionLocation-Talador-List"]={792, 78, 0.*********, 0.774414, 0.469727, 0.545898, false, false},
		["GarrMissionLocation-TannanJungle-List"]={792, 78, 0.*********, 0.774414, 0.547852, 0.624023, false, false},
		["_GarrMissionLocation-BlackrockMountain-List"]={792, 78, 0, 0.773438, 0.*********, 0.0771484, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationListBgs
	["Interface/Garrison/GarrisonMissionLocationNagrand"]={
		["_GarrMissionLocation-Nagrand-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-Nagrand-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-Nagrand-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationNagrand
	["Interface/Garrison/GarrisonMissionLocationNagrandSea"]={
		["_GarrMissionLocation-NagrandSea-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-NagrandSea-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-NagrandSea-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationNagrandSea
	["Interface/Garrison/GarrisonMissionLocationShadowmoonSea"]={
		["_GarrMissionLocation-ShadowmoonSea-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-ShadowmoonSea-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-ShadowmoonSea-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationShadowmoonSea
	["Interface/Garrison/GarrisonMissionLocationShadowmoonValley"]={
		["_GarrMissionLocation-ShadowmoonValley-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-ShadowmoonValley-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-ShadowmoonValley-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationShadowmoonValley
	["Interface/Garrison/GarrisonMissionLocationSpiresofArak"]={
		["_GarrMissionLocation-SpiresofArak-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-SpiresofArak-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-SpiresofArak-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationSpiresofArak
	["Interface/Garrison/GarrisonMissionLocationStormheim"]={
		["_GarrMissionLocation-Stormheim-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationStormheim
	["Interface/Garrison/GarrisonMissionLocationSuramar"]={
		["_GarrMissionLocation-Suramar-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationSuramar
	["Interface/Garrison/GarrisonMissionLocationTalador"]={
		["_GarrMissionLocation-Talador-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-Talador-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-Talador-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationTalador
	["Interface/Garrison/GarrisonMissionLocationTannanJungle"]={
		["_GarrMissionLocation-TannanJungle-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-TannanJungle-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-TannanJungle-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationTannanJungle
	["Interface/Garrison/GarrisonMissionLocationTannanSea"]={
		["_GarrMissionLocation-TannanSea-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-TannanSea-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-TannanSea-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationTannanSea
	["Interface/Garrison/GarrisonMissionLocationTownAlliance"]={
		["_GarrMissionLocation-TownAlliance-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-TownAlliance-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-TownAlliance-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationTownAlliance
	["Interface/Garrison/GarrisonMissionLocationTownHorde"]={
		["_GarrMissionLocation-TownHorde-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-TownHorde-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-TownHorde-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationTownHorde
	["Interface/Garrison/GarrisonMissionLocationUnderground"]={
		["_GarrMissionLocation-Underground-Back"]={1024, 285, 0, 1, 0.*********, 0.279297, true, false},
		["_GarrMissionLocation-Underground-Fore"]={1024, 285, 0, 1, 0.28125, 0.55957, true, false},
		["_GarrMissionLocation-Underground-Mid"]={1024, 285, 0, 1, 0.561523, 0.839844, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationUnderground
	["Interface/Garrison/GarrisonMissionLocationValsharah"]={
		["_GarrMissionLocation-Valsharah-Mid"]={1024, 285, 0, 1, 0.********, 0.558594, true, false},
	}, -- Interface/Garrison/GarrisonMissionLocationValsharah
	["Interface/Garrison/GarrisonMissionParchment"]={
		["GarrMission_MissionParchment"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/GarrisonMissionParchment
	["Interface/Garrison/GarrisonMissionTypeIcons"]={
		["GarrMission_MissionIcon-Blacksmithing"]={64, 64, 0.130859, 0.255859, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Combat"]={64, 64, 0.259766, 0.384766, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Exploration"]={64, 64, 0.********, 0.126953, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Enchanting"]={64, 64, 0.646484, 0.771484, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Salvage"]={64, 64, 0.517578, 0.642578, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Provision"]={64, 64, 0.259766, 0.384766, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Generic"]={64, 64, 0.********, 0.126953, 0.259766, 0.384766, false, false},
		["GarrMission_MissionIcon-Siege"]={64, 64, 0.646484, 0.771484, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Alchemy"]={64, 64, 0.********, 0.126953, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Wildlife"]={64, 64, 0.130859, 0.255859, 0.517578, 0.642578, false, false},
		["GarrMission_MissionIcon-Tailoring"]={64, 64, 0.775391, 0.900391, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Training"]={64, 64, 0.130859, 0.255859, 0.388672, 0.513672, false, false},
		["GarrMission_MissionIcon-Trading"]={64, 64, 0.130859, 0.255859, 0.259766, 0.384766, false, false},
		["GarrMission_MissionIcon-Jewelcrafting"]={64, 64, 0.********, 0.126953, 0.517578, 0.642578, false, false},
		["GarrMission_MissionIcon-Defense"]={64, 64, 0.517578, 0.642578, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Construction"]={64, 64, 0.388672, 0.513672, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Inscription"]={64, 64, 0.********, 0.126953, 0.388672, 0.513672, false, false},
		["GarrMission_MissionIcon-Logistics"]={64, 64, 0.********, 0.126953, 0.775391, 0.900391, false, false},
		["GarrMission_MissionIcon-Engineering"]={64, 64, 0.775391, 0.900391, 0.********, 0.126953, false, false},
		["GarrMission_MissionIcon-Patrol"]={64, 64, 0.130859, 0.255859, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Recruit"]={64, 64, 0.388672, 0.513672, 0.130859, 0.255859, false, false},
		["GarrMission_MissionIcon-Leatherworking"]={64, 64, 0.********, 0.126953, 0.646484, 0.771484, false, false},
		["ShipMissionIcon-Combat-Mission"]={64, 64, 0.130859, 0.255859, 0.775391, 0.900391, false, false},
		["ShipMissionIcon-Oil-Mission"]={64, 64, 0.388672, 0.513672, 0.259766, 0.384766, false, false},
		["ShipMissionIcon-SiegeA-Mission"]={64, 64, 0.517578, 0.642578, 0.259766, 0.384766, false, false},
		["ShipMissionIcon-SiegeH-Mission"]={64, 64, 0.646484, 0.771484, 0.259766, 0.384766, false, false},
		["ShipMissionIcon-Training-Mission"]={64, 64, 0.259766, 0.384766, 0.517578, 0.642578, false, false},
		["ShipMissionIcon-Treasure-Mission"]={64, 64, 0.259766, 0.384766, 0.646484, 0.771484, false, false},
		["ShipMissionIcon-Bonus-Mission"]={64, 64, 0.130859, 0.255859, 0.646484, 0.771484, false, false},
		["ShipMissionIcon-Legendary-Mission"]={64, 64, 0.259766, 0.384766, 0.259766, 0.384766, false, false},
		["ShipMissionIcon-SiegeIHA-Mission"]={64, 64, 0.775391, 0.900391, 0.259766, 0.384766, false, false},
		["ShipMissionIcon-SiegeIHH-Mission"]={64, 64, 0.259766, 0.384766, 0.388672, 0.513672, false, false},
	}, -- Interface/Garrison/GarrisonMissionTypeIcons
	["Interface/Garrison/GarrisonMissionUI1"]={
		["GarrMission_FollowerListButton"]={232, 56, 0.0683594, 0.521484, 0.782227, 0.836914, false, false},
		["GarrMission_FollowerListButton-Highlight"]={232, 56, 0.458984, 0.912109, 0.550781, 0.605469, false, false},
		["GarrMission_FollowerListButton-Select"]={232, 56, 0.********, 0.455078, 0.550781, 0.605469, false, false},
		["GarrMission_FollowerPageHeaderBG"]={32, 77, 0.********, 0.0644531, 0.782227, 0.857422, false, false},
		["GarrMission_IconLevelBG"]={120, 65, 0.660156, 0.894531, 0.702148, 0.765625, false, false},
		["GarrMission_ListGlow-Highlight"]={400, 50, 0.********, 0.783203, 0.651367, 0.700195, false, false},
		["GarrMission_ListGlow-Select"]={400, 50, 0.********, 0.783203, 0.366211, 0.415039, false, false},
		["GarrMission_PlayerLootFrame"]={49, 49, 0.902344, 0.998047, 0.492188, 0.540039, false, false},
		["GarrMission_RareOverlay"]={128, 35, 0.744141, 0.994141, 0.304688, 0.338867, false, false},
		["GarrMission_TopBorderCorner"]={26, 33, 0.916016, 0.966797, 0.550781, 0.583008, false, false},
		["GarrMission_TopBorderCorner-Highlight"]={26, 33, 0.917969, 0.96875, 0.366211, 0.398438, false, false},
		["GarrMission_TopBorderCorner-Select"]={26, 33, 0.673828, 0.724609, 0.298828, 0.331055, false, false},
		["GarrMission_UsefulAbilityIcon"]={34, 37, 0.673828, 0.740234, 0.226562, 0.262695, false, false},
		["GarrMission_XPBarBig-End"]={15, 15, 0.507812, 0.537109, 0.195312, 0.209961, false, false},
		["GarrMission_XPBarBig-LevelBorder"]={28, 15, 0.416016, 0.470703, 0.195312, 0.209961, false, false},
		["GarrMission_XPBarBig-Mid"]={16, 15, 0.285156, 0.316406, 0.195312, 0.209961, false, false},
		["Garr_InfoBoxBorderMission-Corner"]={30, 30, 0.353516, 0.412109, 0.195312, 0.224609, false, false},
		["PartySizeIcon"]={52, 42, 0.796875, 0.898438, 0.492188, 0.533203, false, false},
		["Garr_InfoBoxMission-Corner"]={135, 135, 0.********, 0.265625, 0.416992, 0.548828, false, false},
		["_Garr_InfoBoxMission-Top"]={256, 135, 0, 0.5, 0.0195312, 0.151367, true, false},
		["_GarrMission_MissionListTopHighlight"]={256, 17, 0, 0.5, 0.*********, 0.0175781, true, false},
		["Garr_Mission_MaterialFrame"]={316, 43, 0.********, 0.619141, 0.607422, 0.649414, false, false},
		["GarrMission_RewardsListBG"]={218, 75, 0.570312, 0.996094, 0.416992, 0.490234, false, false},
		["GarrMission_RewardsShadow"]={54, 54, 0.6875, 0.792969, 0.492188, 0.544922, false, false},
		["Garr_MissionList-CostBG"]={143, 30, 0.********, 0.28125, 0.195312, 0.224609, false, false},
		["Garr_MissionList-IconBG"]={150, 76, 0.570312, 0.863281, 0.838867, 0.913086, false, false},
		["GarrMission_ParchmentHeaderSelect-End"]={65, 41, 0.787109, 0.914062, 0.366211, 0.40625, false, false},
		["_GarrMission_ParchmentHeaderSelect-Mid"]={128, 41, 0, 0.25, 0.15332, 0.193359, true, false},
		["GarrMission-FollowerItemBg"]={181, 41, 0.623047, 0.976562, 0.607422, 0.647461, false, false},
		["GarrMission_MissionStart"]={222, 40, 0.525391, 0.958984, 0.782227, 0.821289, false, false},
		["GarrFollower-Shadow"]={342, 141, 0.********, 0.669922, 0.226562, 0.364258, false, false},
		["GarrMission_MissionTooltipAway"]={15, 15, 0.474609, 0.503906, 0.195312, 0.209961, false, false},
		["GarrMission_MissionTooltipWorking"]={15, 15, 0.320312, 0.349609, 0.195312, 0.209961, false, false},
		["GarrMission-AbilityHighlight"]={58, 58, 0.570312, 0.683594, 0.492188, 0.548828, false, false},
		["ShipMission_FollowerListButton-Highlight"]={255, 80, 0.0683594, 0.566406, 0.918945, 0.99707, false, false},
		["ShipMission_FollowerListButton-Select"]={255, 80, 0.0683594, 0.566406, 0.838867, 0.916992, false, false},
		["ShipMission_FollowerListButton"]={255, 80, 0.********, 0.5, 0.702148, 0.780273, false, false},
		["ShipMission_IconLevelBG"]={91, 65, 0.726562, 0.904297, 0.918945, 0.982422, false, false},
		["ShipMission_TopBorderCorner"]={26, 33, 0.673828, 0.724609, 0.264648, 0.296875, false, false},
		["ShipMission_FollowerBG-Glow"]={152, 128, 0.269531, 0.566406, 0.416992, 0.541992, false, false},
		["ShipMission_ShipFollower-EquipmentBG"]={78, 78, 0.570312, 0.722656, 0.918945, 0.995117, false, false},
		["ShipMission_ShipFollower-EquipmentFrame"]={78, 78, 0.503906, 0.65625, 0.702148, 0.77832, false, false},
		["ShipMission_ShipFollower-Lock-Epic"]={48, 61, 0.898438, 0.992188, 0.702148, 0.761719, false, false},
		["ShipMission_ShipFollower-Lock-Rare"]={48, 61, 0.900391, 0.994141, 0.226562, 0.286133, false, false},
		["ShipMission_ShipFollower-TypeFrame"]={78, 78, 0.744141, 0.896484, 0.226562, 0.302734, false, false},
		["GarrMission-AbilityHighlight-Error"]={58, 58, 0.867188, 0.980469, 0.838867, 0.895508, false, false},
	}, -- Interface/Garrison/GarrisonMissionUI1
	["Interface/Garrison/GarrisonMissionUI2"]={
		["GarrMission_Bg-DarkEdgeCorner"]={46, 256, 0.262695, 0.307617, 0.626953, 0.876953, false, false},
		["GarrMission_CounterCheck"]={25, 22, 0.493164, 0.517578, 0.282227, 0.303711, false, false},
		["GarrMission_EncounterAbilityBorder"]={32, 32, 0.674805, 0.706055, 0.424805, 0.456055, false, false},
		["GarrMission_EncounterBar-BG"]={32, 13, 0.630859, 0.662109, 0.282227, 0.294922, false, false},
		["GarrMission_EncounterBar-End"]={17, 17, 0.697266, 0.713867, 0.282227, 0.298828, false, false},
		["GarrMission_EncounterBar-Fill"]={32, 13, 0.664062, 0.695312, 0.282227, 0.294922, false, false},
		["GarrMission_EncounterBar-PortraitRing"]={64, 61, 0.871094, 0.933594, 0.791992, 0.851562, false, false},
		["GarrMission_EncounterBar-Spark"]={27, 27, 0.825195, 0.851562, 0.424805, 0.451172, false, false},
		["GarrMission_EncounterBar-Xbg"]={93, 96, 0.422852, 0.513672, 0.898438, 0.992188, false, false},
		["GarrMission_EncounterBar-Xleft"]={93, 96, 0.639648, 0.730469, 0.755859, 0.849609, false, false},
		["GarrMission_FollowerAbilityRollout"]={243, 75, 0.*********, 0.238281, 0.864258, 0.9375, false, false},
		["GarrMission_FollowerPartyBox_Lg"]={232, 72, 0.422852, 0.649414, 0.613281, 0.683594, false, false},
		["GarrMission_FollowerPartyBox_Sm"]={169, 72, 0.422852, 0.587891, 0.755859, 0.826172, false, false},
		["GarrMission_ItemFrame"]={109, 41, 0.892578, 0.999023, 0.685547, 0.725586, false, false},
		["GarrMission_LevelUpBanner"]={140, 70, 0.422852, 0.55957, 0.828125, 0.896484, false, false},
		["GarrMission_LocationCover-Side"]={49, 230, 0.356445, 0.404297, 0.626953, 0.851562, false, false},
		["GarrMission_ParchmentHeader-End"]={65, 41, 0.925781, 0.989258, 0.918945, 0.958984, false, false},
		["GarrMission_PartyBuffsBG"]={256, 33, 0.493164, 0.743164, 0.248047, 0.280273, false, false},
		["GarrMission_PortraitRing"]={59, 62, 0.935547, 0.993164, 0.791992, 0.852539, false, false},
		["GarrMission_PortraitRing_Empty"]={59, 62, 0.806641, 0.864258, 0.918945, 0.979492, false, false},
		["GarrMission_PortraitRing_Enemy"]={53, 53, 0.944336, 0.996094, 0.385742, 0.4375, false, false},
		["GarrMission_PortraitRing_LevelBorder"]={58, 24, 0.84082, 0.897461, 0.280273, 0.303711, false, false},
		["GarrMission_RareGlowBG"]={128, 35, 0.806641, 0.931641, 0.755859, 0.790039, false, false},
		["GarrMission_RewardsBanner-Desaturate"]={233, 70, 0.422852, 0.650391, 0.685547, 0.753906, false, false},
		["GarrMission_RewardsBanner"]={233, 70, 0.651367, 0.878906, 0.613281, 0.681641, false, false},
		["GarrMission_RewardsBG-Desaturate"]={502, 154, 0.493164, 0.983398, 0.*********, 0.151367, false, false},
		["GarrMission_RewardsBG"]={502, 154, 0.*********, 0.491211, 0.*********, 0.151367, false, false},
		["GarrMission_RewardsBorder-Corner-Desaturate"]={64, 64, 0.732422, 0.794922, 0.908203, 0.970703, false, false},
		["GarrMission_RewardsBorder-Corner"]={64, 64, 0.356445, 0.418945, 0.917969, 0.980469, false, false},
		["GarrMission_Silhouettes-1Alliance"]={266, 284, 0.*********, 0.260742, 0.305664, 0.583008, false, false},
		["GarrMission_Silhouettes-1Horde"]={266, 284, 0.*********, 0.260742, 0.584961, 0.862305, false, false},
		["GarrMission_SingleModelBG"]={162, 170, 0.262695, 0.420898, 0.458984, 0.625, false, false},
		["GarrMission_StartMissionFrame"]={215, 39, 0.*********, 0.210938, 0.939453, 0.977539, false, false},
		["GarrMission_PartyBuffAbilityBorder"]={32, 32, 0.708008, 0.739258, 0.424805, 0.456055, false, false},
		["GarrMission_MissionCostStartButtonBg"]={376, 51, 0.422852, 0.790039, 0.458984, 0.508789, false, false},
		["GarMission_ChestPercentageShadow"]={72, 50, 0.639648, 0.709961, 0.947266, 0.996094, false, false},
		["GarrMission_ChestOpenTextGlow"]={189, 42, 0.791992, 0.976562, 0.458984, 0.5, false, false},
		["GarrMission_PortraitRing_Glow"]={59, 62, 0.866211, 0.923828, 0.856445, 0.916992, false, false},
		["GarrMission_PortraitRing_Highlight"]={59, 62, 0.925781, 0.983398, 0.856445, 0.916992, false, false},
		["GarrMission_CheckGlow"]={32, 32, 0.212891, 0.244141, 0.939453, 0.970703, false, false},
		["GarrMission_EncounterBar-Xright"]={93, 96, 0.639648, 0.730469, 0.851562, 0.945312, false, false},
		["Garr_MissionFX-Glow"]={49, 80, 0.944336, 0.992188, 0.305664, 0.383789, false, false},
		["Garr_MissionFX-Lines"]={27, 84, 0.262695, 0.289062, 0.878906, 0.960938, false, false},
		["GarrMission-AllianceChest"]={209, 155, 0.262695, 0.466797, 0.305664, 0.457031, false, false},
		["GarrMission-HordeChest"]={209, 155, 0.46875, 0.672852, 0.305664, 0.457031, false, false},
		["GarrMission_PortraitRing_Darkener"]={59, 62, 0.806641, 0.864258, 0.856445, 0.916992, false, false},
		["GarrMission_PortraitRing_Quality"]={59, 62, 0.866211, 0.923828, 0.918945, 0.979492, false, false},
		["GarrMission_EncounterBar-CheckMark-Left"]={74, 76, 0.732422, 0.804688, 0.755859, 0.830078, false, false},
		["GarrMission_EncounterBar-CheckMark-Right"]={74, 76, 0.732422, 0.804688, 0.832031, 0.90625, false, false},
		["GarrMission_EncounterBar-CheckMark"]={74, 76, 0.515625, 0.587891, 0.898438, 0.972656, false, false},
		["GarrMission_EncounterBar-CheckMarkCircleSwipe"]={64, 64, 0.356445, 0.418945, 0.853516, 0.916016, false, false},
		["MissionFX-Smoke"]={80, 80, 0.920898, 0.999023, 0.15332, 0.231445, false, false},
		["MissionFX-SparkLines"]={120, 120, 0.825195, 0.942383, 0.305664, 0.422852, false, false},
		["GarrMission_TabGlow"]={189, 42, 0.791992, 0.976562, 0.510742, 0.551758, false, false},
		["BonusChest-GreenGlow-Wide"]={244, 64, 0.652344, 0.890625, 0.685547, 0.748047, false, false},
		["GarrMission_PortraitRing_iLvlBorder"]={70, 24, 0.770508, 0.838867, 0.280273, 0.303711, false, false},
		["GarrMission_EncounterAbilityBorder-Lg"]={46, 46, 0.30957, 0.354492, 0.878906, 0.923828, false, false},
		["GarrMission_EncounterBar-Elite"]={75, 67, 0.920898, 0.994141, 0.233398, 0.298828, false, false},
		["GarrMission_RewardsBox_Shadow"]={345, 50, 0.422852, 0.759766, 0.5625, 0.611328, false, false},
		["GarrMission_MissionCompleteChanceBg"]={282, 95, 0.493164, 0.768555, 0.15332, 0.246094, false, false},
		["GarrMission_CurrentEncounter-Glow"]={70, 70, 0.880859, 0.949219, 0.613281, 0.681641, false, false},
		["GarrMission_CurrentEncounter-SpikeyGlow"]={152, 120, 0.674805, 0.823242, 0.305664, 0.422852, false, false},
		["GarrMission_CounterHalfCheck"]={25, 22, 0.519531, 0.543945, 0.282227, 0.303711, false, false},
		["ShipMission_Bg-DarkEdgeCorner"]={46, 256, 0.30957, 0.354492, 0.626953, 0.876953, false, false},
		["ShipMission_BoatRarity-Epic"]={27, 20, 0.545898, 0.572266, 0.282227, 0.301758, false, false},
		["ShipMission_BoatRarity-Rare"]={27, 20, 0.574219, 0.600586, 0.282227, 0.301758, false, false},
		["ShipMission_BoatRarity-Uncommon"]={27, 20, 0.602539, 0.628906, 0.282227, 0.301758, false, false},
		["ShipMission_DangerousSkull"]={17, 18, 0.745117, 0.761719, 0.248047, 0.265625, false, false},
		["ShipMission_EncounterAbilityBorder-Lg"]={46, 46, 0.951172, 0.996094, 0.613281, 0.658203, false, false},
		["ShipMission_EncounterAbilityBorder"]={32, 32, 0.741211, 0.772461, 0.424805, 0.456055, false, false},
		["ShipMission_LocationCover-Side"]={49, 230, 0.589844, 0.637695, 0.755859, 0.980469, false, false},
		["ShipMission_MissionCostStartButtonBg"]={376, 51, 0.422852, 0.790039, 0.510742, 0.560547, false, false},
		["ShipMission_RewardsBG-Desaturate"]={502, 154, 0.*********, 0.491211, 0.15332, 0.303711, false, false},
		["ShipMission_RewardsBorder-Corner-Desaturate"]={64, 64, 0.806641, 0.869141, 0.791992, 0.854492, false, false},
		["ShipMission_StartMissionFrame"]={215, 39, 0.761719, 0.97168, 0.5625, 0.600586, false, false},
		["ShipMission_FollowerBG"]={152, 128, 0.770508, 0.918945, 0.15332, 0.27832, false, false},
		["GarrMission_WeakEncounterAbilityBorder-Lg"]={46, 46, 0.30957, 0.354492, 0.925781, 0.970703, false, false},
		["GarrMission_WeakEncounterAbilityBorder"]={32, 32, 0.774414, 0.805664, 0.424805, 0.456055, false, false},
	}, -- Interface/Garrison/GarrisonMissionUI2
	["Interface/Garrison/GarrisonMissionUIHorizontal"]={
		["_GarrMission_Bg-BottomEdge"]={256, 251, 0, 1, 0.*********, 0.246094, true, false},
		["_GarrMission_Bg-BottomEdgeSmall"]={256, 42, 0, 1, 0.495117, 0.536133, true, false},
		["_GarrMission_LocationCover-Bottom"]={256, 38, 0, 1, 0.623047, 0.660156, true, false},
		["_GarrMission_ParchmentHeader-Mid"]={128, 41, 0, 0.5, 0.581055, 0.621094, true, false},
		["_GarrMission_RewardsBorder-Top-Desaturate"]={64, 25, 0, 0.25, 0.761719, 0.786133, true, false},
		["_GarrMission_RewardsBorder-Top"]={64, 25, 0, 0.25, 0.735352, 0.759766, true, false},
		["_GarrMission_TopBorder-Highlight"]={32, 10, 0, 0.125, 0.845703, 0.855469, true, false},
		["_GarrMission_TopBorder-Select"]={32, 10, 0, 0.125, 0.857422, 0.867188, true, false},
		["_GarrMission_TopBorder"]={32, 10, 0, 0.125, 0.833984, 0.84375, true, false},
		["_GarrMission_XPBar-BG"]={32, 6, 0, 0.125, 0.880859, 0.886719, true, false},
		["_GarrMission_XPBar-Fill"]={32, 6, 0, 0.125, 0.888672, 0.894531, true, false},
		["_Garr_InfoBoxBorderMission-Top"]={256, 18, 0, 1, 0.814453, 0.832031, true, false},
		["_ShipMission_Bg-BottomEdge"]={256, 251, 0, 1, 0.248047, 0.493164, true, false},
		["_ShipMission_Bg-BottomEdgeSmall"]={256, 42, 0, 1, 0.538086, 0.579102, true, false},
		["_ShipMission_LocationCover-Bottom"]={256, 38, 0, 1, 0.662109, 0.699219, true, false},
		["_ShipMission_RewardsBorder-Top-Desaturate"]={64, 25, 0, 0.25, 0.788086, 0.8125, true, false},
		["_ShipMission_TopBorder"]={32, 10, 0, 0.125, 0.869141, 0.878906, true, false},
		["ShipMission_NameBG"]={132, 21, 0.********, 0.519531, 0.896484, 0.916992, false, false},
		["ShipMission_PartyBuffsBG"]={256, 33, 0, 1, 0.701172, 0.733398, true, false},
	}, -- Interface/Garrison/GarrisonMissionUIHorizontal
	["Interface/Garrison/GarrisonMissionUIInfoBoxBackgroundTile"]={
		["Garr_InfoBoxMission-BackgroundTile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/GarrisonMissionUIInfoBoxBackgroundTile
	["Interface/Garrison/GarrisonMissionUIVertical"]={
		["!GarrMission_Bg-Edge"]={46, 256, 0.269531, 0.359375, 0, 1, false, true},
		["!GarrMission_RewardsBorder-Left-Desaturate"]={30, 64, 0.519531, 0.578125, 0, 0.25, false, true},
		["!GarrMission_RewardsBorder-Left"]={30, 64, 0.457031, 0.515625, 0, 0.25, false, true},
		["!Garr_InfoBoxBorderMission-Left"]={18, 256, 0.644531, 0.679688, 0, 1, false, true},
		["!Garr_InfoBoxMission-Left"]={135, 256, 0.********, 0.265625, 0, 1, false, true},
		["!ShipMission_Bg-Edge"]={46, 256, 0.363281, 0.453125, 0, 1, false, true},
		["!ShipMission_RewardsBorder-Left-Desaturate"]={30, 64, 0.582031, 0.640625, 0, 0.25, false, true},
	}, -- Interface/Garrison/GarrisonMissionUIVertical
	["Interface/Garrison/GarrisonMissionUIVertical2"]={
		["!GarrMission_Bg-DarkEdge"]={334, 256, 0.*********, 0.327148, 0, 1, false, true},
		["!ShipMission_Bg-DarkEdge"]={334, 256, 0.329102, 0.655273, 0, 1, false, true},
	}, -- Interface/Garrison/GarrisonMissionUIVertical2
	["Interface/Garrison/GarrisonMonuments"]={
		["Monuments-Frame"]={333, 91, 0.********, 0.652344, 0.0078125, 0.71875, false, false},
		["Monuments-LeftButton-Down"]={35, 35, 0.910156, 0.978516, 0.0078125, 0.28125, false, false},
		["Monuments-LeftButton-Up"]={35, 35, 0.748047, 0.816406, 0.460938, 0.734375, false, false},
		["Monuments-RightButton-Down"]={35, 35, 0.820312, 0.888672, 0.460938, 0.734375, false, false},
		["Monuments-RightButton-Up"]={35, 35, 0.892578, 0.960938, 0.460938, 0.734375, false, false},
		["Monuments-LockedOverlay"]={128, 56, 0.65625, 0.90625, 0.0078125, 0.445312, false, false},
		["Monuments-Lock"]={45, 59, 0.65625, 0.744141, 0.460938, 0.921875, false, false},
	}, -- Interface/Garrison/GarrisonMonuments
	["Interface/Garrison/GarrisonShipMapIcons"]={
		["ShipMissionIcon-Combat-Map"]={64, 64, 0.263672, 0.388672, 0.632812, 0.757812, false, false},
		["ShipMissionIcon-Combat-MapBadge"]={34, 34, 0.177734, 0.244141, 0.835938, 0.902344, false, false},
		["ShipMissionIcon-Oil-Map"]={64, 64, 0.392578, 0.517578, 0.503906, 0.628906, false, false},
		["ShipMissionIcon-Oil-MapBadge"]={34, 34, 0.263672, 0.330078, 0.890625, 0.957031, false, false},
		["ShipMissionIcon-SiegeA-Map"]={64, 64, 0.392578, 0.517578, 0.632812, 0.757812, false, false},
		["ShipMissionIcon-SiegeA-MapBadge"]={34, 34, 0.392578, 0.458984, 0.890625, 0.957031, false, false},
		["ShipMissionIcon-SiegeH-Map"]={64, 64, 0.392578, 0.517578, 0.761719, 0.886719, false, false},
		["ShipMissionIcon-SiegeH-MapBadge"]={34, 34, 0.908203, 0.974609, 0.503906, 0.570312, false, false},
		["ShipMissionIcon-Training-Map"]={64, 64, 0.779297, 0.904297, 0.503906, 0.628906, false, false},
		["ShipMissionIcon-Training-MapBadge"]={34, 34, 0.521484, 0.587891, 0.902344, 0.96875, false, false},
		["ShipMissionIcon-Treasure-Map"]={64, 64, 0.521484, 0.646484, 0.632812, 0.757812, false, false},
		["ShipMissionIcon-Treasure-MapBadge"]={34, 34, 0.650391, 0.716797, 0.632812, 0.699219, false, false},
		["ShipMission-RedGlowRing"]={88, 88, 0.********, 0.173828, 0.765625, 0.9375, false, false},
		["ShipMissionIcon-Bonus-Map"]={64, 64, 0.263672, 0.388672, 0.503906, 0.628906, false, false},
		["ShipMissionIcon-Bonus-MapBadge"]={34, 34, 0.177734, 0.244141, 0.765625, 0.832031, false, false},
		["NavalMap-CircleGlowTrails"]={255, 255, 0.********, 0.5, 0.********, 0.5, false, false},
		["NavalMap-LargeBonusCircle"]={234, 234, 0.503906, 0.960938, 0.********, 0.458984, false, false},
		["NavalMap-SmallBonusCircle"]={132, 132, 0.********, 0.259766, 0.503906, 0.761719, false, false},
		["ShipMission-TimerBG"]={55, 21, 0.********, 0.109375, 0.941406, 0.982422, false, false},
		["ShipMissionIcon-Legendary-Map"]={64, 64, 0.263672, 0.388672, 0.761719, 0.886719, false, false},
		["ShipMissionIcon-Legendary-MapBadge"]={34, 34, 0.177734, 0.244141, 0.90625, 0.972656, false, false},
		["ShipMissionIcon-SiegeIHA-Map"]={64, 64, 0.521484, 0.646484, 0.503906, 0.628906, false, false},
		["ShipMissionIcon-SiegeIHA-MapBadge"]={34, 34, 0.521484, 0.587891, 0.761719, 0.828125, false, false},
		["ShipMissionIcon-SiegeIHH-Map"]={64, 64, 0.650391, 0.775391, 0.503906, 0.628906, false, false},
		["ShipMissionIcon-SiegeIHH-MapBadge"]={34, 34, 0.521484, 0.587891, 0.832031, 0.898438, false, false},
	}, -- Interface/Garrison/GarrisonShipMapIcons
	["Interface/Garrison/GarrisonShipMissionParchment"]={
		["ShipMissionParchment-Tile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/GarrisonShipMissionParchment
	["Interface/Garrison/GarrisonToast"]={
		["Garr_Toast"]={317, 82, 0.********, 0.621094, 0.166016, 0.326172, false, false},
		["Garr_MissionToast"]={317, 82, 0.********, 0.621094, 0.********, 0.162109, false, false},
		["Garr_FollowerToast-Epic"]={219, 26, 0.537109, 0.964844, 0.494141, 0.544922, false, false},
		["Garr_FollowerToast-Rare"]={219, 26, 0.537109, 0.964844, 0.548828, 0.599609, false, false},
		["Garr_FollowerToast-Uncommon"]={219, 26, 0.********, 0.429688, 0.613281, 0.664062, false, false},
		["Garr_MissionToast-IconBG"]={73, 55, 0.625, 0.767578, 0.********, 0.109375, false, false},
		["Garr_MissionToast-Blank"]={272, 59, 0.********, 0.533203, 0.494141, 0.609375, false, false},
		["ShipMission_Toast"]={317, 82, 0.********, 0.621094, 0.330078, 0.490234, false, false},
	}, -- Interface/Garrison/GarrisonToast
	["Interface/Garrison/GarrisonUIAtlas"]={
		["Garr_InfoBox-Corner"]={128, 128, 0.********, 0.503906, 0.541016, 0.791016, false, false},
		["Garr_InfoBoxBorder-BigBottomCorner"]={44, 46, 0.730469, 0.902344, 0.541016, 0.630859, false, false},
		["Garr_InfoBoxBorder-Corner"]={30, 30, 0.351562, 0.46875, 0.794922, 0.853516, false, false},
		["Garr_InfoBoxBorder-FiligreeCorner"]={87, 101, 0.********, 0.34375, 0.794922, 0.992188, false, false},
		["Garr_WoodFrameCorner"]={54, 54, 0.511719, 0.722656, 0.541016, 0.646484, false, false},
		["_Garr_InfoBox-Top"]={256, 135, 0, 1, 0.********, 0.265625, true, false},
		["_Garr_InfoBoxBorder-BigBottom"]={256, 34, 0, 1, 0.269531, 0.335938, true, false},
		["_Garr_InfoBoxBorder-Top"]={256, 18, 0, 1, 0.501953, 0.537109, true, false},
		["_Garr_WoodFrameTile-Bottom"]={256, 28, 0, 1, 0.339844, 0.394531, true, false},
		["_Garr_WoodFrameTile-Top"]={256, 28, 0, 1, 0.398438, 0.453125, true, false},
		["_Garr_XPBar_Mid"]={32, 21, 0, 0.125, 0.457031, 0.498047, true, false},
		["Garr_InfoBox-CornerShadow"]={8, 8, 0.351562, 0.382812, 0.857422, 0.873047, false, false},
	}, -- Interface/Garrison/GarrisonUIAtlas
	["Interface/Garrison/GarrisonUIAtlas2"]={
		["!Garr_InfoBox-Left"]={135, 256, 0.********, 0.53125, 0, 1, false, true},
		["!Garr_InfoBoxBorder-Left"]={18, 256, 0.65625, 0.726562, 0, 1, false, true},
		["!Garr_WoodFrameTile-Left"]={28, 256, 0.539062, 0.648438, 0, 1, false, true},
	}, -- Interface/Garrison/GarrisonUIAtlas2
	["Interface/Garrison/GarrisonUIBackground"]={
		["Garr_InfoBox-BackgroundTile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/GarrisonUIBackground
	["Interface/Garrison/GarrisonUIBackground2"]={
		["Garr_WoodFrame-BackgroundTile"]={256, 256, 0, 1, 0, 1, true, true},
	}, -- Interface/Garrison/GarrisonUIBackground2
	["Interface/Garrison/HeadhunterUI"]={
		["Headhunter_AbilityBorderGlow"]={32, 32, 0.********, 0.0644531, 0.726562, 0.789062, false, false},
		["Headhunter_BG"]={321, 369, 0.********, 0.628906, 0.********, 0.722656, false, false},
		["Headhunter_LineHeader"]={223, 1, 0.0683594, 0.503906, 0.726562, 0.728516, false, false},
	}, -- Interface/Garrison/HeadhunterUI
	["Interface/Garrison/HordeGarrisonTier1"]={
		["Horde_Tier1_Arena1"]={45, 51, 0.371094, 0.458984, 0.777344, 0.976562, false, false},
		["Horde_Tier1_Arena2"]={43, 45, 0.488281, 0.572266, 0.********, 0.179688, false, false},
		["Horde_Tier1_Armory1"]={57, 57, 0.136719, 0.248047, 0.484375, 0.707031, false, false},
		["Horde_Tier1_Armory2"]={56, 55, 0.257812, 0.367188, 0.********, 0.21875, false, false},
		["Horde_Tier1_Barn1"]={51, 51, 0.257812, 0.357422, 0.4375, 0.636719, false, false},
		["Horde_Tier1_Barn2"]={53, 46, 0.371094, 0.474609, 0.199219, 0.378906, false, false},
		["Horde_Tier1_Barracks1"]={65, 70, 0.********, 0.128906, 0.285156, 0.558594, false, false},
		["Horde_Tier1_Barracks2"]={67, 70, 0.********, 0.132812, 0.********, 0.277344, false, false},
		["Horde_Tier1_Farm1"]={58, 48, 0.371094, 0.484375, 0.********, 0.191406, false, false},
		["Horde_Tier1_Fishing1"]={42, 44, 0.488281, 0.570312, 0.1875, 0.359375, false, false},
		["Horde_Tier1_Inn1"]={50, 48, 0.371094, 0.46875, 0.386719, 0.574219, false, false},
		["Horde_Tier1_Inn2"]={50, 48, 0.371094, 0.46875, 0.582031, 0.769531, false, false},
		["Horde_Tier1_Lumber1"]={45, 43, 0.********, 0.0898438, 0.820312, 0.988281, false, false},
		["Horde_Tier1_Lumber2"]={37, 48, 0.488281, 0.560547, 0.714844, 0.902344, false, false},
		["Horde_Tier1_Mage1"]={51, 50, 0.257812, 0.357422, 0.644531, 0.839844, false, false},
		["Horde_Tier1_Mage2"]={52, 52, 0.257812, 0.359375, 0.226562, 0.429688, false, false},
		["Horde_Tier1_Mine1"]={52, 36, 0.257812, 0.359375, 0.847656, 0.988281, false, false},
		["Horde_Tier1_Profession1"]={40, 38, 0.658203, 0.736328, 0.********, 0.152344, false, false},
		["Horde_Tier1_Profession2"]={42, 43, 0.488281, 0.570312, 0.539062, 0.707031, false, false},
		["Horde_Tier1_Profession3"]={43, 42, 0.488281, 0.572266, 0.367188, 0.53125, false, false},
		["Horde_Tier1_Stables1"]={57, 55, 0.136719, 0.248047, 0.714844, 0.929688, false, false},
		["Horde_Tier1_Stables2"]={62, 63, 0.********, 0.123047, 0.566406, 0.8125, false, false},
		["Horde_Tier1_Trading1"]={37, 37, 0.740234, 0.8125, 0.********, 0.148438, false, false},
		["Horde_Tier1_Trading2"]={40, 39, 0.576172, 0.654297, 0.********, 0.15625, false, false},
		["Horde_Tier1_Workshop1"]={60, 60, 0.136719, 0.253906, 0.********, 0.238281, false, false},
		["Horde_Tier1_Workshop2"]={56, 59, 0.136719, 0.246094, 0.246094, 0.476562, false, false},
	}, -- Interface/Garrison/HordeGarrisonTier1
	["Interface/Garrison/HordeGarrisonTier2"]={
		["Horde_Tier2_Arena1"]={59, 55, 0.5625, 0.677734, 0.238281, 0.453125, false, false},
		["Horde_Tier2_Arena2"]={54, 55, 0.451172, 0.556641, 0.511719, 0.726562, false, false},
		["Horde_Tier2_Armory1"]={64, 68, 0.173828, 0.298828, 0.578125, 0.84375, false, false},
		["Horde_Tier2_Armory2"]={65, 61, 0.320312, 0.447266, 0.********, 0.242188, false, false},
		["Horde_Tier2_Barn1"]={59, 58, 0.683594, 0.798828, 0.********, 0.230469, false, false},
		["Horde_Tier2_Barn2"]={53, 45, 0.5625, 0.666016, 0.664062, 0.839844, false, false},
		["Horde_Tier2_Barracks1"]={61, 60, 0.320312, 0.439453, 0.25, 0.484375, false, false},
		["Horde_Tier2_Barracks2"]={61, 60, 0.320312, 0.439453, 0.492188, 0.726562, false, false},
		["Horde_Tier2_Inn1"]={60, 57, 0.802734, 0.919922, 0.********, 0.226562, false, false},
		["Horde_Tier2_Inn2"]={60, 58, 0.5625, 0.679688, 0.********, 0.230469, false, false},
		["Horde_Tier2_Lumber1"]={55, 66, 0.451172, 0.558594, 0.********, 0.261719, false, false},
		["Horde_Tier2_Lumber2"]={54, 60, 0.451172, 0.556641, 0.269531, 0.503906, false, false},
		["Horde_Tier2_Mage1"]={76, 76, 0.********, 0.150391, 0.652344, 0.949219, false, false},
		["Horde_Tier2_Mage2"]={73, 74, 0.173828, 0.316406, 0.********, 0.292969, false, false},
		["Horde_Tier2_Stables1"]={62, 59, 0.320312, 0.441406, 0.734375, 0.964844, false, false},
		["Horde_Tier2_Stables2"]={65, 69, 0.173828, 0.300781, 0.300781, 0.570312, false, false},
		["Horde_Tier2_Trading1"]={54, 52, 0.451172, 0.556641, 0.734375, 0.9375, false, false},
		["Horde_Tier2_Trading2"]={54, 50, 0.5625, 0.667969, 0.460938, 0.65625, false, false},
		["Horde_Tier2_Workshop1"]={79, 77, 0.********, 0.15625, 0.34375, 0.644531, false, false},
		["Horde_Tier2_Workshop2"]={86, 85, 0.********, 0.169922, 0.********, 0.335938, false, false},
	}, -- Interface/Garrison/HordeGarrisonTier2
	["Interface/Garrison/HordeGarrisonTier3"]={
		["Horde_Tier3_Arena1"]={70, 65, 0.476562, 0.613281, 0.********, 0.257812, false, false},
		["Horde_Tier3_Arena2"]={55, 69, 0.736328, 0.84375, 0.265625, 0.535156, false, false},
		["Horde_Tier3_Armory1"]={65, 71, 0.322266, 0.449219, 0.613281, 0.890625, false, false},
		["Horde_Tier3_Armory2"]={65, 63, 0.605469, 0.732422, 0.265625, 0.511719, false, false},
		["Horde_Tier3_Barn1"]={64, 65, 0.476562, 0.601562, 0.265625, 0.519531, false, false},
		["Horde_Tier3_Barn2"]={72, 50, 0.847656, 0.988281, 0.265625, 0.460938, false, false},
		["Horde_Tier3_Barracks1"]={76, 88, 0.********, 0.150391, 0.335938, 0.679688, false, false},
		["Horde_Tier3_Barracks2"]={82, 76, 0.********, 0.162109, 0.6875, 0.984375, false, false},
		["Horde_Tier3_Inn1"]={70, 65, 0.617188, 0.753906, 0.********, 0.257812, false, false},
		["Horde_Tier3_Inn2"]={60, 67, 0.476562, 0.59375, 0.527344, 0.789062, false, false},
		["Horde_Tier3_Lumber1"]={68, 62, 0.757812, 0.890625, 0.********, 0.246094, false, false},
		["Horde_Tier3_Lumber2"]={59, 51, 0.476562, 0.591797, 0.796875, 0.996094, false, false},
		["Horde_Tier3_Mage1"]={76, 75, 0.169922, 0.318359, 0.679688, 0.972656, false, false},
		["Horde_Tier3_Mage2"]={73, 73, 0.322266, 0.464844, 0.320312, 0.605469, false, false},
		["Horde_Tier3_Stables1"]={77, 79, 0.322266, 0.472656, 0.********, 0.3125, false, false},
		["Horde_Tier3_Stables2"]={76, 83, 0.169922, 0.318359, 0.347656, 0.671875, false, false},
		["Horde_Tier3_Trading1"]={66, 54, 0.736328, 0.865234, 0.542969, 0.753906, false, false},
		["Horde_Tier3_Trading2"]={63, 63, 0.605469, 0.728516, 0.519531, 0.765625, false, false},
		["Horde_Tier3_Workshop1"]={76, 86, 0.169922, 0.318359, 0.********, 0.339844, false, false},
		["Horde_Tier3_Workshop2"]={84, 83, 0.********, 0.166016, 0.********, 0.328125, false, false},
	}, -- Interface/Garrison/HordeGarrisonTier3
	["Interface/Garrison/LegionMission"]={
		["legionmission-EncounterAbilityBorder-Lg"]={61, 61, 0.********, 0.242188, 0.617188, 0.855469, false, false},
		["legionmission-EncounterAbilityBorder"]={35, 35, 0.828125, 0.964844, 0.********, 0.140625, false, false},
		["GarrisonTroops-Health-Consume"]={15, 13, 0.894531, 0.953125, 0.148438, 0.199219, false, false},
		["GarrisonTroops-Health"]={15, 13, 0.828125, 0.886719, 0.148438, 0.199219, false, false},
		["legionmission-hearts-background"]={119, 44, 0.523438, 0.988281, 0.617188, 0.789062, false, false},
		["GarrMission-NeutralChest"]={209, 155, 0.********, 0.820312, 0.********, 0.609375, false, false},
		["legionmission-icon-currency"]={44, 41, 0.523438, 0.695312, 0.796875, 0.957031, false, false},
		["legionmission-lock"]={32, 42, 0.828125, 0.953125, 0.207031, 0.371094, false, false},
		["legionmission-portraitring-epicplus"]={68, 61, 0.25, 0.515625, 0.617188, 0.855469, false, false},
		["legionmission-portraitring_levelborder_epicplus"]={58, 24, 0.********, 0.230469, 0.863281, 0.957031, false, false},
	}, -- Interface/Garrison/LegionMission
	["Interface/Garrison/LegionMissionCombatAllyBackground"]={
		["legionmission-background-combatally"]={918, 619, 0.*********, 0.897461, 0.*********, 0.605469, false, false},
	}, -- Interface/Garrison/LegionMissionCombatAllyBackground
	["Interface/Garrison/LegionMissionMap"]={
		["LegionMissionIcon-ZoneSupportAlliance-Map"]={64, 64, 0.********, 0.253906, 0.********, 0.253906, false, false},
		["LegionMissionIcon-ZoneSupportAlliance-MapBadge"]={34, 34, 0.777344, 0.910156, 0.********, 0.136719, false, false},
		["LegionMissionIcon-ZoneSupportHorde-Map"]={64, 64, 0.519531, 0.769531, 0.********, 0.253906, false, false},
		["LegionMissionIcon-ZoneSupportHorde-MapBadge"]={34, 34, 0.********, 0.136719, 0.519531, 0.652344, false, false},
		["LegionMissionIcon-ZoneSupportAlliance-Mission"]={64, 64, 0.261719, 0.511719, 0.********, 0.253906, false, false},
		["LegionMissionIcon-ZoneSupportHorde-Mission"]={64, 64, 0.********, 0.253906, 0.261719, 0.511719, false, false},
	}, -- Interface/Garrison/LegionMissionMap
	["Interface/Garrison/LegionMissionMapOrderHalls"]={
		["legionmission-map-orderhall-deathknight"]={99, 66, 0.********, 0.195312, 0.********, 0.261719, false, false},
		["legionmission-map-orderhall-demonhunter"]={99, 66, 0.********, 0.195312, 0.535156, 0.792969, false, false},
		["legionmission-map-orderhall-druid"]={99, 66, 0.********, 0.195312, 0.269531, 0.527344, false, false},
		["legionmission-map-orderhall-glow"]={142, 109, 0.396484, 0.673828, 0.269531, 0.695312, false, false},
		["legionmission-map-orderhall-hunter"]={99, 66, 0.199219, 0.392578, 0.********, 0.261719, false, false},
		["legionmission-map-orderhall-mage"]={99, 66, 0.199219, 0.392578, 0.269531, 0.527344, false, false},
		["legionmission-map-orderhall-monk"]={99, 66, 0.199219, 0.392578, 0.535156, 0.792969, false, false},
		["legionmission-map-orderhall-paladin"]={99, 66, 0.396484, 0.589844, 0.********, 0.261719, false, false},
		["legionmission-map-orderhall-priest"]={99, 66, 0.59375, 0.787109, 0.********, 0.261719, false, false},
		["legionmission-map-orderhall-rogue"]={99, 66, 0.791016, 0.984375, 0.********, 0.261719, false, false},
		["legionmission-map-orderhall-shaman"]={99, 66, 0.677734, 0.871094, 0.269531, 0.527344, false, false},
		["legionmission-map-orderhall-warlock"]={99, 66, 0.677734, 0.871094, 0.535156, 0.792969, false, false},
		["legionmission-map-orderhall-warrior"]={99, 66, 0.396484, 0.589844, 0.703125, 0.960938, false, false},
		["legionmission-map-orderhall-textglow"]={122, 22, 0.677734, 0.916016, 0.800781, 0.886719, false, false},
	}, -- Interface/Garrison/LegionMissionMapOrderHalls
	["Interface/Garrison/MobileAppIcons"]={
		["Mobile-BonusIcon"]={128, 128, 0.381836, 0.506836, 0.*********, 0.125977, false, false},
		["Mobile-CombatBadgeIcon"]={128, 128, 0.635742, 0.760742, 0.*********, 0.125977, false, false},
		["Mobile-CombatIcon"]={128, 128, 0.762695, 0.887695, 0.*********, 0.125977, false, false},
		["Mobile-LegendaryQuestIcon"]={128, 128, 0.635742, 0.760742, 0.12793, 0.25293, false, false},
		["Mobile-QuestIcon"]={128, 128, 0.381836, 0.506836, 0.254883, 0.379883, false, false},
		["Mobile-TreasureIcon"]={128, 128, 0.254883, 0.379883, 0.381836, 0.506836, false, false},
		["Mobile-CombatIcon-Desaturated"]={128, 128, 0.*********, 0.125977, 0.12793, 0.25293, false, false},
		["Mobile-BonusIcon-Desaturated"]={128, 128, 0.508789, 0.633789, 0.*********, 0.125977, false, false},
		["Mobile-TreasureIcon-Desaturated"]={128, 128, 0.254883, 0.379883, 0.508789, 0.633789, false, false},
		["Mobile-QuestIcon-Desaturated"]={128, 128, 0.508789, 0.633789, 0.254883, 0.379883, false, false},
		["Mobile-LegendaryQuestIcon-Desaturated"]={128, 128, 0.762695, 0.887695, 0.12793, 0.25293, false, false},
		["Mobile-Alchemy"]={128, 128, 0.*********, 0.125977, 0.*********, 0.125977, false, false},
		["Mobile-Archeology"]={128, 128, 0.12793, 0.25293, 0.*********, 0.125977, false, false},
		["Mobile-Blacksmithing"]={128, 128, 0.254883, 0.379883, 0.*********, 0.125977, false, false},
		["Mobile-Cooking"]={128, 128, 0.*********, 0.125977, 0.254883, 0.379883, false, false},
		["Mobile-Enchanting"]={128, 128, 0.*********, 0.125977, 0.381836, 0.506836, false, false},
		["Mobile-Enginnering"]={128, 128, 0.*********, 0.125977, 0.508789, 0.633789, false, false},
		["Mobile-FirstAid"]={128, 128, 0.*********, 0.125977, 0.635742, 0.760742, false, false},
		["Mobile-Fishing"]={128, 128, 0.*********, 0.125977, 0.762695, 0.887695, false, false},
		["Mobile-Herbalism"]={128, 128, 0.12793, 0.25293, 0.12793, 0.25293, false, false},
		["Mobile-Inscription"]={128, 128, 0.254883, 0.379883, 0.12793, 0.25293, false, false},
		["Mobile-Jewelcrafting"]={128, 128, 0.381836, 0.506836, 0.12793, 0.25293, false, false},
		["Mobile-Leatherworking"]={128, 128, 0.508789, 0.633789, 0.12793, 0.25293, false, false},
		["Mobile-MechanicIcon-Curse"]={128, 128, 0.12793, 0.25293, 0.254883, 0.379883, false, false},
		["Mobile-MechanicIcon-Disorienting"]={128, 128, 0.12793, 0.25293, 0.381836, 0.506836, false, false},
		["Mobile-MechanicIcon-Lethal"]={128, 128, 0.12793, 0.25293, 0.508789, 0.633789, false, false},
		["Mobile-MechanicIcon-Slowing"]={128, 128, 0.12793, 0.25293, 0.635742, 0.760742, false, false},
		["Mobile-Mining"]={128, 128, 0.12793, 0.25293, 0.762695, 0.887695, false, false},
		["Mobile-Pets"]={128, 128, 0.254883, 0.379883, 0.254883, 0.379883, false, false},
		["Mobile-Skinning"]={128, 128, 0.635742, 0.760742, 0.254883, 0.379883, false, false},
		["Mobile-Tailoring"]={128, 128, 0.762695, 0.887695, 0.254883, 0.379883, false, false},
		["Mobile-MechanicIcon-Powerful"]={128, 128, 0.254883, 0.379883, 0.635742, 0.760742, false, false},
	}, -- Interface/Garrison/MobileAppIcons
	["Interface/Garrison/OrderHallCommandBarHorizontal"]={
		["_orderhall-commandbar-bg"]={128, 25, 0, 1, 0.015625, 0.40625, true, false},
		["orderhall-commandbar-iconmask"]={57, 23, 0.0078125, 0.453125, 0.4375, 0.796875, false, false},
		["orderhall-commandbar-mapbutton-down"]={27, 27, 0.46875, 0.679688, 0.4375, 0.859375, false, false},
		["orderhall-commandbar-mapbutton-up"]={27, 27, 0.695312, 0.90625, 0.4375, 0.859375, false, false},
	}, -- Interface/Garrison/OrderHallCommandBarHorizontal
	["Interface/Garrison/OrderHallLandingButtonDeathKnight"]={
		["legionmission-landingbutton-deathknight-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-deathknight-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonDeathKnight
	["Interface/Garrison/OrderHallLandingButtonDemonHunter"]={
		["legionmission-landingbutton-demonhunter-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-demonhunter-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonDemonHunter
	["Interface/Garrison/OrderHallLandingButtonDruid"]={
		["legionmission-landingbutton-druid-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-druid-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonDruid
	["Interface/Garrison/OrderHallLandingButtonHunter"]={
		["legionmission-landingbutton-hunter-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-hunter-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonHunter
	["Interface/Garrison/OrderHallLandingButtonMage"]={
		["legionmission-landingbutton-mage-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-mage-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonMage
	["Interface/Garrison/OrderHallLandingButtonMonk"]={
		["legionmission-landingbutton-monk-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-monk-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonMonk
	["Interface/Garrison/OrderHallLandingButtonPaladin"]={
		["legionmission-landingbutton-paladin-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-paladin-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonPaladin
	["Interface/Garrison/OrderHallLandingButtonPriest"]={
		["legionmission-landingbutton-priest-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-priest-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonPriest
	["Interface/Garrison/OrderHallLandingButtonRogue"]={
		["legionmission-landingbutton-rogue-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-rogue-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonRogue
	["Interface/Garrison/OrderHallLandingButtonShaman"]={
		["legionmission-landingbutton-shaman-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-shaman-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonShaman
	["Interface/Garrison/OrderHallLandingButtonWarlock"]={
		["legionmission-landingbutton-warlock-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-warlock-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonWarlock
	["Interface/Garrison/OrderHallLandingButtonWarrior"]={
		["legionmission-landingbutton-warrior-down"]={53, 53, 0.0078125, 0.421875, 0.015625, 0.84375, false, false},
		["legionmission-landingbutton-warrior-up"]={53, 53, 0.4375, 0.851562, 0.015625, 0.84375, false, false},
	}, -- Interface/Garrison/OrderHallLandingButtonWarrior
	["Interface/Garrison/OrderHallLandingPageDeathKnight"]={
		["legionmission-landingpage-background-deathknight"]={229, 184, 0.********, 0.898438, 0.********, 0.722656, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageDeathKnight
	["Interface/Garrison/OrderHallLandingPageDemonHunter"]={
		["legionmission-landingpage-background-demonhunter"]={230, 236, 0.********, 0.902344, 0.********, 0.925781, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageDemonHunter
	["Interface/Garrison/OrderHallLandingPageDruid"]={
		["legionmission-landingpage-background-druid"]={230, 219, 0.********, 0.902344, 0.********, 0.859375, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageDruid
	["Interface/Garrison/OrderHallLandingPageHunter"]={
		["legionmission-landingpage-background-hunter"]={225, 228, 0.********, 0.882812, 0.********, 0.894531, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageHunter
	["Interface/Garrison/OrderHallLandingPageMage"]={
		["legionmission-landingpage-background-mage"]={230, 203, 0.********, 0.902344, 0.********, 0.796875, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageMage
	["Interface/Garrison/OrderHallLandingPageMonk"]={
		["legionmission-landingpage-background-monk"]={221, 249, 0.********, 0.867188, 0.********, 0.976562, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageMonk
	["Interface/Garrison/OrderHallLandingPagePaladin"]={
		["legionmission-landingpage-background-paladin"]={200, 250, 0.********, 0.785156, 0.********, 0.980469, false, false},
	}, -- Interface/Garrison/OrderHallLandingPagePaladin
	["Interface/Garrison/OrderHallLandingPagePriest"]={
		["legionmission-landingpage-background-priest"]={230, 190, 0.********, 0.902344, 0.********, 0.746094, false, false},
	}, -- Interface/Garrison/OrderHallLandingPagePriest
	["Interface/Garrison/OrderHallLandingPageRogue"]={
		["legionmission-landingpage-background-rogue"]={231, 211, 0.********, 0.90625, 0.********, 0.828125, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageRogue
	["Interface/Garrison/OrderHallLandingPageShaman"]={
		["legionmission-landingpage-background-shaman"]={209, 250, 0.********, 0.820312, 0.********, 0.980469, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageShaman
	["Interface/Garrison/OrderHallLandingPageWarlock"]={
		["legionmission-landingpage-background-warlock"]={230, 184, 0.********, 0.902344, 0.********, 0.722656, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageWarlock
	["Interface/Garrison/OrderHallLandingPageWarrior"]={
		["legionmission-landingpage-background-warrior"]={181, 253, 0.********, 0.710938, 0.********, 0.992188, false, false},
	}, -- Interface/Garrison/OrderHallLandingPageWarrior
	["Interface/Garrison/OrderHallMissionCompleteDeathKnight"]={
		["legionmission-complete-background-deathknight"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteDeathKnight
	["Interface/Garrison/OrderHallMissionCompleteDemonHunter"]={
		["legionmission-complete-background-demonhunter"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteDemonHunter
	["Interface/Garrison/OrderHallMissionCompleteDruid"]={
		["legionmission-complete-background-druid"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteDruid
	["Interface/Garrison/OrderHallMissionCompleteHunter"]={
		["legionmission-complete-background-hunter"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteHunter
	["Interface/Garrison/OrderHallMissionCompleteMage"]={
		["legionmission-complete-background-mage"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteMage
	["Interface/Garrison/OrderHallMissionCompleteMonk"]={
		["legionmission-complete-background-monk"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteMonk
	["Interface/Garrison/OrderHallMissionCompletePaladin"]={
		["legionmission-complete-background-paladin"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompletePaladin
	["Interface/Garrison/OrderHallMissionCompletePriest"]={
		["legionmission-complete-background-priest"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompletePriest
	["Interface/Garrison/OrderHallMissionCompleteRogue"]={
		["legionmission-complete-background-rogue"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteRogue
	["Interface/Garrison/OrderHallMissionCompleteShaman"]={
		["legionmission-complete-background-shaman"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteShaman
	["Interface/Garrison/OrderHallMissionCompleteWarlock"]={
		["legionmission-complete-background-warlock"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteWarlock
	["Interface/Garrison/OrderHallMissionCompleteWarrior"]={
		["legionmission-complete-background-warrior"]={690, 333, 0.*********, 0.674805, 0.********, 0.652344, false, false},
	}, -- Interface/Garrison/OrderHallMissionCompleteWarrior
	["Interface/Garrison/OrderHallTalents"]={
		["orderhalltalents-choice-background"]={163, 59, 0.291016, 0.609375, 0.********, 0.234375, false, false},
		["orderhalltalents-spellborder-green"]={50, 50, 0.130859, 0.228516, 0.785156, 0.980469, false, false},
		["orderhalltalents-spellborder-yellow"]={50, 50, 0.291016, 0.388672, 0.242188, 0.4375, false, false},
		["orderhalltalents-spellborder"]={50, 50, 0.130859, 0.228516, 0.582031, 0.777344, false, false},
		["orderhalltalents-timer-bg"]={64, 64, 0.********, 0.126953, 0.582031, 0.832031, false, false},
		["orderhalltalents-choice-arrow-large"]={47, 27, 0.291016, 0.382812, 0.445312, 0.550781, false, false},
		["orderhalltalents-choice-background-on"]={163, 59, 0.613281, 0.931641, 0.********, 0.234375, false, false},
		["orderhalltalents-done-checkmark"]={40, 35, 0.********, 0.0800781, 0.839844, 0.976562, false, false},
		["orderhalltalents-done-glow"]={146, 146, 0.********, 0.287109, 0.********, 0.574219, false, false},
	}, -- Interface/Garrison/OrderHallTalents
	["Interface/Garrison/OrderHallTalentsDeathKnight"]={
		["orderhalltalents-background-deathknight"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsDeathKnight
	["Interface/Garrison/OrderHallTalentsDemonHunter"]={
		["orderhalltalents-background-demonhunter"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsDemonHunter
	["Interface/Garrison/OrderHallTalentsDruid"]={
		["orderhalltalents-background-druid"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsDruid
	["Interface/Garrison/OrderHallTalentsHunter"]={
		["orderhalltalents-background-hunter"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsHunter
	["Interface/Garrison/OrderHallTalentsMage"]={
		["orderhalltalents-background-mage"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsMage
	["Interface/Garrison/OrderHallTalentsMonk"]={
		["orderhalltalents-background-monk"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsMonk
	["Interface/Garrison/OrderHallTalentsPaladin"]={
		["orderhalltalents-background-paladin"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsPaladin
	["Interface/Garrison/OrderHallTalentsPriest"]={
		["orderhalltalents-background-priest"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsPriest
	["Interface/Garrison/OrderHallTalentsRogue"]={
		["orderhalltalents-background-rogue"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsRogue
	["Interface/Garrison/OrderHallTalentsShaman"]={
		["orderhalltalents-background-shaman"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsShaman
	["Interface/Garrison/OrderHallTalentsWarlock"]={
		["orderhalltalents-background-warlock"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsWarlock
	["Interface/Garrison/OrderHallTalentsWarrior"]={
		["orderhalltalents-background-warrior"]={322, 382, 0.********, 0.630859, 0.********, 0.748047, false, false},
	}, -- Interface/Garrison/OrderHallTalentsWarrior
	["Interface/Garrison/ShipsCargoShipMap"]={
		["Ships_CargoShip-Map"]={94, 94, 0.0078125, 0.742188, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/ShipsCargoShipMap
	["Interface/Garrison/ShipsCargoShipMission"]={
		["Ships_CargoShip-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_CargoShip-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsCargoShipMission
	["Interface/Garrison/ShipsCarrierAMap"]={
		["Ships_CarrierA-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsCarrierAMap
	["Interface/Garrison/ShipsCarrierAMission"]={
		["Ships_CarrierA-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_CarrierA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsCarrierAMission
	["Interface/Garrison/ShipsCarrierHMap"]={
		["Ships_CarrierH-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsCarrierHMap
	["Interface/Garrison/ShipsCarrierHMission"]={
		["Ships_CarrierH-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_CarrierH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsCarrierHMission
	["Interface/Garrison/ShipsCarrierMap"]={
		["Ships_Carrier-Map"]={94, 94, 0.0078125, 0.742188, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/ShipsCarrierMap
	["Interface/Garrison/ShipsCarrierMission"]={
		["Ships_Carrier-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_Carrier-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsCarrierMission
	["Interface/Garrison/ShipsDreadnaughtAMap"]={
		["Ships_DreadnaughtA-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsDreadnaughtAMap
	["Interface/Garrison/ShipsDreadnaughtAMission"]={
		["Ships_DreadnaughtA-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_DreadnaughtA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsDreadnaughtAMission
	["Interface/Garrison/ShipsDreadnaughtHMap"]={
		["Ships_DreadnaughtH-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsDreadnaughtHMap
	["Interface/Garrison/ShipsDreadnaughtHMission"]={
		["Ships_DreadnaughtH-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_DreadnaughtH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsDreadnaughtHMission
	["Interface/Garrison/ShipsDreadnaughtMap"]={
		["Ships_Dreadnaught-Map"]={94, 94, 0.0078125, 0.742188, 0.0078125, 0.742188, false, false},
	}, -- Interface/Garrison/ShipsDreadnaughtMap
	["Interface/Garrison/ShipsDreadnaughtMission"]={
		["Ships_Dreadnaught-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_Dreadnaught-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsDreadnaughtMission
	["Interface/Garrison/ShipsEnemyCargoShipMission"]={
		["Ships_EnemyCargoShip-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyCargoShipMission
	["Interface/Garrison/ShipsEnemyCarrierAMission"]={
		["Ships_EnemyCarrierA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyCarrierAMission
	["Interface/Garrison/ShipsEnemyCarrierHMission"]={
		["Ships_EnemyCarrierH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyCarrierHMission
	["Interface/Garrison/ShipsEnemyCarrierMission"]={
		["Ships_EnemyCarrier-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyCarrierMission
	["Interface/Garrison/ShipsEnemyDreadnaughtAMission"]={
		["Ships_EnemyDreadnaughtA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyDreadnaughtAMission
	["Interface/Garrison/ShipsEnemyDreadnaughtHMission"]={
		["Ships_EnemyDreadnaughtH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyDreadnaughtHMission
	["Interface/Garrison/ShipsEnemyDreadnaughtMission"]={
		["Ships_EnemyDreadnaught-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyDreadnaughtMission
	["Interface/Garrison/ShipsEnemyGalleonAMission"]={
		["Ships_EnemyGalleonA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyGalleonAMission
	["Interface/Garrison/ShipsEnemyGalleonHMission"]={
		["Ships_EnemyGalleonH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyGalleonHMission
	["Interface/Garrison/ShipsEnemySubmarineAMission"]={
		["Ships_EnemySubmarineA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemySubmarineAMission
	["Interface/Garrison/ShipsEnemySubmarineHMission"]={
		["Ships_EnemySubmarineH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemySubmarineHMission
	["Interface/Garrison/ShipsEnemyTroopTransportMission"]={
		["Ships_EnemyTroopTransport-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsEnemyTroopTransportMission
	["Interface/Garrison/ShipsGalleonAMap"]={
		["Ships_GalleonA-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsGalleonAMap
	["Interface/Garrison/ShipsGalleonAMission"]={
		["Ships_GalleonA-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_GalleonA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsGalleonAMission
	["Interface/Garrison/ShipsGalleonHMap"]={
		["Ships_GalleonH-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsGalleonHMap
	["Interface/Garrison/ShipsGalleonHMission"]={
		["Ships_GalleonH-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_GalleonH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsGalleonHMission
	["Interface/Garrison/ShipsSubmarineAMap"]={
		["Ships_SubmarineA-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsSubmarineAMap
	["Interface/Garrison/ShipsSubmarineAMission"]={
		["Ships_SubmarineA-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_SubmarineA-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsSubmarineAMission
	["Interface/Garrison/ShipsSubmarineHMap"]={
		["Ships_SubmarineH-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsSubmarineHMap
	["Interface/Garrison/ShipsSubmarineHMission"]={
		["Ships_SubmarineH-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_SubmarineH-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsSubmarineHMission
	["Interface/Garrison/ShipsTroopTransportMap"]={
		["Ships_TroopTransport-Map"]={83, 72, 0.0078125, 0.65625, 0.0078125, 0.570312, false, false},
	}, -- Interface/Garrison/ShipsTroopTransportMap
	["Interface/Garrison/ShipsTroopTransportMission"]={
		["Ships_TroopTransport-List"]={154, 80, 0.********, 0.605469, 0.511719, 0.824219, false, false},
		["Ships_TroopTransport-Portrait"]={152, 128, 0.********, 0.597656, 0.********, 0.503906, false, false},
	}, -- Interface/Garrison/ShipsTroopTransportMission
	["Interface/Garrison/ShipyardNavalMap01"]={
		["NavalMap01"]={934, 630, 0.*********, 0.913086, 0.*********, 0.616211, false, false},
		["NavalMap-Horde-Fog"]={496, 361, 0.*********, 0.485352, 0.618164, 0.970703, false, false},
		["NavalMap-Horde-Highlight"]={496, 361, 0.487305, 0.97168, 0.618164, 0.970703, false, false},
	}, -- Interface/Garrison/ShipyardNavalMap01
	["Interface/Garrison/ShipyardNavalMap02"]={
		["NavalMap-Alliance-Fog"]={449, 351, 0.*********, 0.439453, 0.*********, 0.34375, false, false},
		["NavalMap-IronHorde-Fog"]={454, 353, 0.*********, 0.444336, 0.345703, 0.69043, false, false},
		["NavalMap-OpenWaters-Fog"]={452, 306, 0.446289, 0.887695, 0.692383, 0.991211, false, false},
		["NavalMap-Alliance-Highlight"]={449, 351, 0.441406, 0.879883, 0.*********, 0.34375, false, false},
		["NavalMap-IronHorde-Highlight"]={454, 353, 0.446289, 0.889648, 0.345703, 0.69043, false, false},
		["NavalMap-OpenWaters-Highlight"]={452, 306, 0.*********, 0.442383, 0.692383, 0.991211, false, false},
	}, -- Interface/Garrison/ShipyardNavalMap02
	["Interface/Garrison/TalentTreeChromie"]={
		["talenttree-chromie-background"]={322, 438, 0.********, 0.630859, 0.********, 0.857422, false, false},
	}, -- Interface/Garrison/TalentTreeChromie
	["Interface/Garrison/ZoneAbilityGeneric"]={
		["ZoneAbility-Generic-AbilityBar-Glow"]={153, 27, 0.308594, 0.90625, 0.234375, 0.445312, false, false},
		["ZoneAbility-Generic-AbilityBar"]={153, 27, 0.308594, 0.90625, 0.0078125, 0.21875, false, false},
		["ZoneAbility-Generic-IconBorder"]={63, 65, 0.308594, 0.554688, 0.460938, 0.96875, false, false},
		["ZoneAbility-Generic-SpellBox"]={76, 66, 0.********, 0.300781, 0.0078125, 0.523438, false, false},
	}, -- Interface/Garrison/ZoneAbilityGeneric
	["Interface/Glues/AccountUpgrade/AccountUpgradeBanners"]={
		["accountupgradebanner-bc"]={198, 99, 0.396484, 0.783203, 0.511719, 0.705078, false, false},
		["accountupgradebanner-cataclysm"]={200, 140, 0.********, 0.392578, 0.********, 0.275391, false, false},
		["accountupgradebanner-classic"]={200, 100, 0.********, 0.392578, 0.744141, 0.939453, false, false},
		["accountupgradebanner-mop"]={200, 117, 0.396484, 0.787109, 0.279297, 0.507812, false, false},
		["accountupgradebanner-wod"]={200, 117, 0.********, 0.392578, 0.511719, 0.740234, false, false},
		["accountupgradebanner-wotlk"]={200, 140, 0.396484, 0.787109, 0.********, 0.275391, false, false},
		["accountupgradebanner-legion"]={200, 117, 0.********, 0.392578, 0.279297, 0.507812, false, false},
	}, -- Interface/Glues/AccountUpgrade/AccountUpgradeBanners
	["Interface/Glues/AccountUpgrade/ClassTrialThanksFrame"]={
		["ClassTrial-End-Frame"]={563, 345, 0.*********, 0.550781, 0.*********, 0.337891, false, false},
		["ClassTrial-Hunter-Ring"]={188, 188, 0.*********, 0.18457, 0.339844, 0.523438, false, false},
		["ClassTrial-Priest-Ring"]={188, 188, 0.186523, 0.370117, 0.525391, 0.708984, false, false},
		["ClassTrial-DeathKnight-Ring"]={188, 188, 0.552734, 0.736328, 0.*********, 0.18457, false, false},
		["ClassTrial-Warrior-Ring"]={188, 188, 0.557617, 0.741211, 0.339844, 0.523438, false, false},
		["ClassTrial-Paladin-Ring"]={188, 188, 0.186523, 0.370117, 0.339844, 0.523438, false, false},
		["ClassTrial-Warlock-Ring"]={188, 188, 0.37207, 0.555664, 0.339844, 0.523438, false, false},
		["ClassTrial-Druid-Ring"]={188, 188, 0.738281, 0.921875, 0.*********, 0.18457, false, false},
		["ClassTrial-Monk-Ring"]={188, 188, 0.*********, 0.18457, 0.710938, 0.894531, false, false},
		["ClassTrial-Mage-Ring"]={188, 188, 0.*********, 0.18457, 0.525391, 0.708984, false, false},
		["ClassTrial-Rogue-Ring"]={188, 188, 0.186523, 0.370117, 0.710938, 0.894531, false, false},
		["ClassTrial-Shaman-Ring"]={188, 188, 0.743164, 0.926758, 0.339844, 0.523438, false, false},
	}, -- Interface/Glues/AccountUpgrade/ClassTrialThanksFrame
	["Interface/Glues/AccountUpgrade/ClassTrialTimer"]={
		["ClassTrial-Timer-Mid"]={17, 81, 0, 0.0332031, 0.********, 0.320312, true, false},
		["ClassTrial-Timer-RightCorner"]={25, 81, 0.550781, 0.599609, 0.328125, 0.644531, false, false},
		["ClassTrial-Timer"]={279, 81, 0.********, 0.546875, 0.328125, 0.644531, false, false},
	}, -- Interface/Glues/AccountUpgrade/ClassTrialTimer
	["Interface/Glues/CharacterCreate/AlliedRacesAllianceandHordeBanner"]={
		["AlliedRaces-AllianceHordeBanner"]={212, 520, 0.********, 0.832031, 0.*********, 0.508789, false, false},
	}, -- Interface/Glues/CharacterCreate/AlliedRacesAllianceandHordeBanner
	["Interface/Glues/CharacterCreate/BlizzconCC2015"]={
		["LegionSplash-NewCharacter"]={822, 362, 0.*********, 0.803711, 0.*********, 0.354492, false, false},
		["LegionSplash-DemonHunterButton"]={504, 84, 0.491211, 0.983398, 0.356445, 0.438477, false, false},
		["LegionSplash-Logo"]={500, 238, 0.*********, 0.489258, 0.356445, 0.588867, false, false},
		["LegionSplash-NewCharacterButton"]={465, 84, 0.491211, 0.945312, 0.44043, 0.522461, false, false},
	}, -- Interface/Glues/CharacterCreate/BlizzconCC2015
	["Interface/Glues/CharacterCreate/BlizzconCC2015DH"]={
		["LegionSplash-DemonHunter"]={942, 759, 0.*********, 0.920898, 0.*********, 0.742188, false, false},
	}, -- Interface/Glues/CharacterCreate/BlizzconCC2015DH
	["Interface/Glues/CharacterCreate/CharacterCreate"]={
		["charactercreate-banner-bottom"]={200, 64, 0.396484, 0.787109, 0.********, 0.253906, false, false},
		["charactercreate-banner-middle"]={200, 194, 0.********, 0.392578, 0.********, 0.761719, false, false},
		["charactercreate-banner-top"]={200, 49, 0.********, 0.392578, 0.769531, 0.960938, false, false},
	}, -- Interface/Glues/CharacterCreate/CharacterCreate
	["Interface/Glues/CharacterCreate/CharacterCreateClassTrial"]={
		["UI-CharacterCreate-LargeButton-Blue-Highlight"]={108, 37, 0.********, 0.425781, 0.0078125, 0.296875, false, false},
		["UI-CharacterCreate-LargeButton-Yellow-Highlight"]={108, 37, 0.********, 0.425781, 0.3125, 0.601562, false, false},
		["UI-CharacterCreate-LargeButton"]={108, 41, 0.********, 0.425781, 0.617188, 0.9375, false, false},
		["UI-CharacterCreate-Metal-Finery-Corner"]={56, 49, 0.433594, 0.652344, 0.3125, 0.695312, false, false},
		["UI-CharacterCreate-Metal-Small-Finery"]={35, 32, 0.433594, 0.570312, 0.0078125, 0.257812, false, false},
		["UI-CharacterCreate-PadLock"]={63, 76, 0.660156, 0.90625, 0.3125, 0.90625, false, false},
	}, -- Interface/Glues/CharacterCreate/CharacterCreateClassTrial
	["Interface/Glues/CharacterCreate/CharacterCreateIcons"]={
		["raceicon-bloodelf-male"]={64, 64, 0.130859, 0.255859, 0.********, 0.126953, false, false},
		["raceicon-draenei-female"]={64, 64, 0.259766, 0.384766, 0.********, 0.126953, false, false},
		["raceicon-draenei-male"]={64, 64, 0.388672, 0.513672, 0.********, 0.126953, false, false},
		["raceicon-dwarf-female"]={64, 64, 0.517578, 0.642578, 0.********, 0.126953, false, false},
		["raceicon-dwarf-male"]={64, 64, 0.646484, 0.771484, 0.********, 0.126953, false, false},
		["raceicon-gnome-female"]={64, 64, 0.775391, 0.900391, 0.********, 0.126953, false, false},
		["raceicon-gnome-male"]={64, 64, 0.********, 0.126953, 0.130859, 0.255859, false, false},
		["raceicon-goblin-male"]={64, 64, 0.********, 0.126953, 0.388672, 0.513672, false, false},
		["raceicon-human-female"]={64, 64, 0.********, 0.126953, 0.775391, 0.900391, false, false},
		["raceicon-human-male"]={64, 64, 0.130859, 0.255859, 0.130859, 0.255859, false, false},
		["raceicon-nightelf-female"]={64, 64, 0.775391, 0.900391, 0.130859, 0.255859, false, false},
		["raceicon-nightelf-male"]={64, 64, 0.130859, 0.255859, 0.259766, 0.384766, false, false},
		["raceicon-orc-female"]={64, 64, 0.130859, 0.255859, 0.388672, 0.513672, false, false},
		["raceicon-orc-male"]={64, 64, 0.130859, 0.255859, 0.517578, 0.642578, false, false},
		["raceicon-pandaren-female"]={64, 64, 0.130859, 0.255859, 0.646484, 0.771484, false, false},
		["raceicon-pandaren-male"]={64, 64, 0.130859, 0.255859, 0.775391, 0.900391, false, false},
		["raceicon-tauren-female"]={64, 64, 0.259766, 0.384766, 0.259766, 0.384766, false, false},
		["raceicon-tauren-male"]={64, 64, 0.388672, 0.513672, 0.259766, 0.384766, false, false},
		["raceicon-troll-female"]={64, 64, 0.517578, 0.642578, 0.259766, 0.384766, false, false},
		["raceicon-troll-male"]={64, 64, 0.646484, 0.771484, 0.259766, 0.384766, false, false},
		["raceicon-undead-female"]={64, 64, 0.775391, 0.900391, 0.259766, 0.384766, false, false},
		["raceicon-undead-male"]={64, 64, 0.259766, 0.384766, 0.388672, 0.513672, false, false},
		["raceicon-worgen-female"]={64, 64, 0.259766, 0.384766, 0.775391, 0.900391, false, false},
		["raceicon-worgen-male"]={64, 64, 0.388672, 0.513672, 0.388672, 0.513672, false, false},
		["raceicon-voidelf-male"]={64, 64, 0.259766, 0.384766, 0.646484, 0.771484, false, false},
		["raceicon-goblin-female"]={64, 64, 0.********, 0.126953, 0.259766, 0.384766, false, false},
		["raceicon-nightborne-male"]={64, 64, 0.646484, 0.771484, 0.130859, 0.255859, false, false},
		["raceicon-lightforged-male"]={64, 64, 0.388672, 0.513672, 0.130859, 0.255859, false, false},
		["raceicon-nightborne-female"]={64, 64, 0.517578, 0.642578, 0.130859, 0.255859, false, false},
		["raceicon-highmountain-female"]={64, 64, 0.********, 0.126953, 0.517578, 0.642578, false, false},
		["raceicon-voidelf-female"]={64, 64, 0.259766, 0.384766, 0.517578, 0.642578, false, false},
		["raceicon-lightforged-female"]={64, 64, 0.259766, 0.384766, 0.130859, 0.255859, false, false},
		["raceicon-bloodelf-female"]={64, 64, 0.********, 0.126953, 0.********, 0.126953, false, false},
		["raceicon-highmountain-male"]={64, 64, 0.********, 0.126953, 0.646484, 0.771484, false, false},
	}, -- Interface/Glues/CharacterCreate/CharacterCreateIcons
	["Interface/Glues/CharacterCreate/CharacterCreateMetalFrameHorizontal"]={
		["UI-CharacterCreate-MetalFrame-Horizontal"]={256, 16, 0, 1, 0.03125, 0.53125, true, false},
	}, -- Interface/Glues/CharacterCreate/CharacterCreateMetalFrameHorizontal
	["Interface/Glues/CharacterCreate/CharacterCreateMetalFrameVertical"]={
		["UI-CharacterCreate-MetalFrame-Vertical"]={16, 256, 0.03125, 0.53125, 0, 1, false, true},
	}, -- Interface/Glues/CharacterCreate/CharacterCreateMetalFrameVertical
	["Interface/Glues/CharacterCreate/NewCharacterNotification"]={
		["NewCharacter-Alliance"]={112, 58, 0.0078125, 0.882812, 0.0078125, 0.460938, false, false},
		["NewCharacter-Horde"]={112, 58, 0.0078125, 0.882812, 0.476562, 0.929688, false, false},
	}, -- Interface/Glues/CharacterCreate/NewCharacterNotification
	["Interface/Glues/CharacterSelect/CharacterUndelete"]={
		["characterundelete-RestoreButton"]={30, 30, 0.03125, 0.96875, 0.03125, 0.96875, false, false},
	}, -- Interface/Glues/CharacterSelect/CharacterUndelete
	["Interface/Glues/CharacterSelect/GlueAnnouncementPopup"]={
		["glueannouncementpopup-arrow"]={64, 47, 0.78418, 0.84668, 0.*********, 0.046875, false, false},
		["glueannouncementpopup-background"]={800, 533, 0.*********, 0.782227, 0.*********, 0.521484, false, false},
		["glueannouncementpopup-icon-info"]={21, 21, 0.848633, 0.869141, 0.*********, 0.0214844, false, false},
		["glueannouncementpopup-inset"]={361, 86, 0.*********, 0.353516, 0.523438, 0.607422, false, false},
	}, -- Interface/Glues/CharacterSelect/GlueAnnouncementPopup
	["Interface/Glues/Common/CinematicButtonsAtlas"]={
		["StreamCinematic-LK-Down"]={176, 78, 0.426758, 0.598633, 0.********, 0.308594, false, false},
		["StreamCinematic-LK-Up"]={176, 78, 0.426758, 0.598633, 0.316406, 0.621094, false, false},
		["StreamCinematic-WOD-Down"]={176, 78, 0.774414, 0.946289, 0.********, 0.308594, false, false},
		["StreamCinematic-WOD-Up"]={176, 78, 0.600586, 0.772461, 0.316406, 0.621094, false, false},
		["StreamCinematic-BC-Down"]={176, 78, 0.*********, 0.172852, 0.********, 0.308594, false, false},
		["StreamCinematic-BC-Up"]={176, 78, 0.*********, 0.172852, 0.316406, 0.621094, false, false},
		["StreamCinematic-CC-Down"]={176, 78, 0.*********, 0.172852, 0.628906, 0.933594, false, false},
		["StreamCinematic-CC-Up"]={176, 78, 0.174805, 0.34668, 0.********, 0.308594, false, false},
		["StreamCinematic-Classic-Down"]={176, 78, 0.174805, 0.34668, 0.316406, 0.621094, false, false},
		["StreamCinematic-Classic-Up"]={176, 78, 0.174805, 0.34668, 0.628906, 0.933594, false, false},
		["StreamCinematic-DownloadIcon"]={43, 43, 0.348633, 0.390625, 0.699219, 0.867188, false, false},
		["StreamCinematic-Highlight"]={78, 176, 0.348633, 0.424805, 0.********, 0.691406, false, false},
		["StreamCinematic-PlayButton"]={43, 43, 0.948242, 0.990234, 0.********, 0.171875, false, false},
		["StreamCinematic-ProgressBarBG"]={10, 4, 0.*********, 0.0107422, 0.941406, 0.957031, false, false},
		["StreamCinematic-MOP-Down"]={176, 78, 0.426758, 0.598633, 0.628906, 0.933594, false, false},
		["StreamCinematic-MOP-Up"]={176, 78, 0.600586, 0.772461, 0.********, 0.308594, false, false},
		["StreamCinematic-Legion-Down"]={176, 78, 0.600586, 0.772461, 0.628906, 0.933594, false, false},
		["StreamCinematic-Legion-Up"]={176, 78, 0.774414, 0.946289, 0.316406, 0.621094, false, false},
	}, -- Interface/Glues/Common/CinematicButtonsAtlas
	["Interface/Glues/Common/LegionLogo"]={
		["Glues-WoW-LegionLogo"]={510, 256, 0.********, 0.998047, 0.*********, 0.250977, false, false},
		["Glues-WoW-LegionLogo_cn"]={510, 256, 0.********, 0.998047, 0.25293, 0.50293, false, false},
		["Glues-WoW-LegionLogo_tw"]={510, 256, 0.********, 0.998047, 0.504883, 0.754883, false, false},
	}, -- Interface/Glues/Common/LegionLogo
	["Interface/GuildFrame/GuildLevelRing"]={
		["guild-levelring"]={33, 30, 0.015625, 0.53125, 0.03125, 0.96875, false, false},
	}, -- Interface/GuildFrame/GuildLevelRing
	["Interface/HelpFrame/NewPlayerExperienceParts"]={
		["NPE_ExclamationPoint"]={32, 32, 0.958008, 0.989258, 0.********, 0.0644531, false, false},
		["NPE_Icon"]={64, 64, 0.0654297, 0.12793, 0.826172, 0.951172, false, false},
		["NPE_LeftClick"]={32, 32, 0.958008, 0.989258, 0.130859, 0.193359, false, false},
		["NPE_RightClick"]={32, 32, 0.958008, 0.989258, 0.259766, 0.322266, false, false},
		["NPE_TurnIn"]={20, 20, 0.958008, 0.977539, 0.0683594, 0.107422, false, false},
		["NPE_ArrowDownGlow"]={64, 64, 0.893555, 0.956055, 0.130859, 0.255859, false, false},
		["NPE_ArrowUpGlow"]={64, 64, 0.0654297, 0.12793, 0.697266, 0.822266, false, false},
		["NPE_ArrowUp"]={64, 64, 0.*********, 0.0634766, 0.826172, 0.951172, false, false},
		["NPE_ArrowDown"]={64, 64, 0.893555, 0.956055, 0.********, 0.126953, false, false},
		["NPE_ArrowRightGlow"]={64, 64, 0.*********, 0.0634766, 0.697266, 0.822266, false, false},
		["NPE_keyboard"]={912, 354, 0.*********, 0.891602, 0.********, 0.693359, false, false},
		["NPE_ArrowRight"]={64, 64, 0.893555, 0.956055, 0.517578, 0.642578, false, false},
		["NPE_ArrowLeftGlow"]={64, 64, 0.893555, 0.956055, 0.388672, 0.513672, false, false},
		["NPE_ArrowLeft"]={64, 64, 0.893555, 0.956055, 0.259766, 0.384766, false, false},
	}, -- Interface/HelpFrame/NewPlayerExperienceParts
	["Interface/ItemTextFrame/Book"]={
		["book-bg"]={486, 494, 0.********, 0.951172, 0.********, 0.966797, false, false},
		["book-line"]={411, 8, 0.********, 0.804688, 0.970703, 0.986328, false, false},
	}, -- Interface/ItemTextFrame/Book
	["Interface/Legionfall/Legionfall"]={
		["Legionfall_Background"]={871, 576, 0.*********, 0.851562, 0.*********, 0.563477, false, false},
		["Legionfall_Banner"]={252, 39, 0.572266, 0.818359, 0.727539, 0.765625, false, false},
		["Legionfall_BarBackground"]={230, 18, 0.591797, 0.816406, 0.84082, 0.858398, false, false},
		["Legionfall_BarFrame"]={240, 31, 0.387695, 0.62207, 0.808594, 0.838867, false, false},
		["Legionfall_BarSpark"]={16, 64, 0.853516, 0.869141, 0.046875, 0.109375, false, false},
		["Legionfall_BuildingInfoBackground"]={207, 30, 0.387695, 0.589844, 0.84082, 0.870117, false, false},
		["Legionfall_GrayFrame"]={205, 97, 0.*********, 0.201172, 0.56543, 0.660156, false, false},
		["Legionfall_Padlock"]={38, 45, 0.853516, 0.890625, 0.*********, 0.0449219, false, false},
		["Legionfall_CommandCenter_Completed"]={187, 81, 0.203125, 0.385742, 0.56543, 0.644531, false, false},
		["Legionfall_CommandCenter_Destroyed"]={187, 81, 0.387695, 0.570312, 0.56543, 0.644531, false, false},
		["Legionfall_CommandCenter_UnderAttack"]={187, 81, 0.572266, 0.754883, 0.56543, 0.644531, false, false},
		["Legionfall_CommandCenter_UnderConstruction"]={187, 81, 0.756836, 0.939453, 0.56543, 0.644531, false, false},
		["Legionfall_MageTower_Completed"]={187, 81, 0.203125, 0.385742, 0.646484, 0.725586, false, false},
		["Legionfall_MageTower_Destroyed"]={187, 81, 0.387695, 0.570312, 0.646484, 0.725586, false, false},
		["Legionfall_MageTower_UnderAttack"]={187, 81, 0.572266, 0.754883, 0.646484, 0.725586, false, false},
		["Legionfall_MageTower_UnderConstruction"]={187, 81, 0.756836, 0.939453, 0.646484, 0.725586, false, false},
		["Legionfall_NetherDisruptor_Completed"]={187, 81, 0.203125, 0.385742, 0.727539, 0.806641, false, false},
		["Legionfall_NetherDisruptor_Destroyed"]={187, 81, 0.203125, 0.385742, 0.808594, 0.887695, false, false},
		["Legionfall_NetherDisruptor_UnderAttack"]={187, 81, 0.203125, 0.385742, 0.889648, 0.96875, false, false},
		["Legionfall_NetherDisruptor_UnderConstruction"]={187, 81, 0.387695, 0.570312, 0.727539, 0.806641, false, false},
		["Legionfall_GrayBanner"]={252, 39, 0.572266, 0.818359, 0.767578, 0.805664, false, false},
		["Legionfall_GreenFrame"]={205, 97, 0.*********, 0.201172, 0.662109, 0.756836, false, false},
		["Legionfall_RedFrame"]={205, 97, 0.*********, 0.201172, 0.758789, 0.853516, false, false},
		["Legionfall_YellowFrame"]={205, 97, 0.*********, 0.201172, 0.855469, 0.950195, false, false},
		["Legionfall_ExitFrame"]={33, 32, 0.932617, 0.964844, 0.*********, 0.0322266, false, false},
		["Legionfall_BarFrame-Glow"]={240, 31, 0.624023, 0.858398, 0.808594, 0.838867, false, false},
		["Legionfall_GrayFrame_Buffs"]={39, 39, 0.892578, 0.930664, 0.*********, 0.0390625, false, false},
	}, -- Interface/Legionfall/Legionfall
	["Interface/Legionfall/LegionfallHorizontal"]={
		["_Legionfall_BarFill_Active"]={256, 17, 0, 1, 0.015625, 0.28125, true, false},
		["_Legionfall_BarFill_UnderAttack"]={256, 17, 0, 1, 0.3125, 0.578125, true, false},
		["_Legionfall_BarFill_UnderConstruction"]={256, 17, 0, 1, 0.609375, 0.875, true, false},
	}, -- Interface/Legionfall/LegionfallHorizontal
	["Interface/LevelUp/BossBanner"]={
		["BossBanner-BottomFillagree"]={66, 28, 0.865234, 0.994141, 0.314453, 0.369141, false, false},
		["BossBanner-SkullCircle"]={44, 44, 0.865234, 0.951172, 0.134766, 0.220703, false, false},
		["BossBanner-TopFillagree"]={176, 74, 0.244141, 0.587891, 0.576172, 0.720703, false, false},
		["BossBanner-RedFlash"]={92, 92, 0.********, 0.181641, 0.810547, 0.990234, false, false},
		["BossBanner-LeftFillagree"]={72, 40, 0.591797, 0.732422, 0.576172, 0.654297, false, false},
		["BossBanner-RightFillagree"]={72, 40, 0.736328, 0.876953, 0.576172, 0.654297, false, false},
		["BossBanner-SkullSpikes"]={50, 66, 0.865234, 0.962891, 0.********, 0.130859, false, false},
		["BossBanner-BgBanner-Bottom"]={440, 112, 0.********, 0.861328, 0.********, 0.220703, false, false},
		["BossBanner-BgBanner-Top"]={440, 112, 0.********, 0.861328, 0.224609, 0.443359, false, false},
		["LootBanner-IconGlow"]={40, 40, 0.865234, 0.943359, 0.447266, 0.525391, false, false},
		["LootBanner-ItemBg"]={269, 41, 0.244141, 0.769531, 0.724609, 0.804688, false, false},
		["LootBanner-LootBagCircle"]={44, 44, 0.865234, 0.951172, 0.224609, 0.310547, false, false},
		["BossBanner-BgBanner-Mid"]={440, 64, 0.********, 0.861328, 0.447266, 0.572266, false, false},
		["BossBanner-RedLightning"]={122, 118, 0.********, 0.240234, 0.576172, 0.806641, false, false},
	}, -- Interface/LevelUp/BossBanner
	["Interface/LevelUp/MinorTalents"]={
		["minortalents-icon-book"]={40, 38, 0.853516, 0.931641, 0.09375, 0.167969, false, false},
		["minortalents-iconcover"]={25, 45, 0.853516, 0.902344, 0.171875, 0.259766, false, false},
		["_minortalents-iconspinner"]={512, 45, 0, 1, 0.********, 0.0898438, true, false},
		["minortalents-backplate"]={418, 169, 0.********, 0.818359, 0.464844, 0.794922, false, false},
		["minortalents-descriptionshadow"]={434, 188, 0.********, 0.849609, 0.09375, 0.460938, false, false},
	}, -- Interface/LevelUp/MinorTalents
	["Interface/LFGFrame/GroupFinder"]={
		["groupfinder-icon-voice"]={16, 14, 0.731445, 0.74707, 0.078125, 0.0917969, false, false},
		["groupfinder-icon-friend"]={20, 19, 0.731445, 0.750977, 0.0380859, 0.0566406, false, false},
		["groupfinder-highlightbar-green"]={304, 32, 0.608398, 0.905273, 0.916016, 0.947266, false, false},
		["groupfinder-highlightbar-yellow"]={304, 32, 0.608398, 0.905273, 0.949219, 0.980469, false, false},
		["groupfinder-icon-greencheckmark"]={11, 13, 0.977539, 0.988281, 0.*********, 0.0136719, false, false},
		["groupfinder-icon-redx"]={12, 12, 0.685547, 0.697266, 0.0488281, 0.0605469, false, false},
		["groupfinder-background"]={328, 336, 0.*********, 0.321289, 0.266602, 0.594727, false, false},
		["groupfinder-button-arenas"]={290, 36, 0.601562, 0.884766, 0.*********, 0.0361328, false, false},
		["groupfinder-button-battlegrounds"]={290, 36, 0.*********, 0.28418, 0.680664, 0.71582, false, false},
		["groupfinder-button-cover"]={300, 46, 0.*********, 0.293945, 0.59668, 0.641602, false, false},
		["groupfinder-button-custom-pve"]={290, 36, 0.571289, 0.854492, 0.133789, 0.168945, false, false},
		["groupfinder-button-custom-pvp"]={290, 36, 0.*********, 0.28418, 0.801758, 0.836914, false, false},
		["groupfinder-button-dungeons"]={290, 36, 0.*********, 0.28418, 0.876953, 0.912109, false, false},
		["groupfinder-button-questing"]={290, 36, 0.*********, 0.28418, 0.764648, 0.799805, false, false},
		["groupfinder-button-raids-bc"]={290, 36, 0.*********, 0.28418, 0.914062, 0.949219, false, false},
		["groupfinder-button-raids-cataclysm"]={290, 36, 0.286133, 0.569336, 0.133789, 0.168945, false, false},
		["groupfinder-button-raids-classic"]={290, 36, 0.608398, 0.891602, 0.84082, 0.875977, false, false},
		["groupfinder-button-raids-mists"]={290, 36, 0.316406, 0.599609, 0.*********, 0.0361328, false, false},
		["groupfinder-button-raids-wrath"]={290, 36, 0.*********, 0.28418, 0.133789, 0.168945, false, false},
		["groupfinder-button-ratedbgs"]={290, 36, 0.323242, 0.606445, 0.84082, 0.875977, false, false},
		["groupfinder-button-scenarios"]={290, 36, 0.323242, 0.606445, 0.916016, 0.951172, false, false},
		["groupfinder-button-skirmishes"]={290, 36, 0.323242, 0.606445, 0.953125, 0.988281, false, false},
		["groupfinder-highlightbar-blue"]={304, 32, 0.*********, 0.297852, 0.951172, 0.982422, false, false},
		["groupfinder-icon-leader"]={14, 9, 0.685547, 0.699219, 0.0380859, 0.046875, false, false},
		["groupfinder-eye-highlight"]={86, 86, 0.75293, 0.836914, 0.0380859, 0.12207, false, false},
		["groupfinder-button-highlight"]={292, 37, 0.*********, 0.286133, 0.838867, 0.875, false, false},
		["groupfinder-button-select"]={292, 37, 0.323242, 0.608398, 0.87793, 0.914062, false, false},
		["groupfinder-button-cover-down"]={300, 46, 0.*********, 0.293945, 0.717773, 0.762695, false, false},
		["groupfinder-icon-class-deathknight"]={29, 29, 0.838867, 0.867188, 0.0683594, 0.0966797, false, false},
		["groupfinder-icon-class-druid"]={29, 29, 0.328125, 0.356445, 0.0986328, 0.126953, false, false},
		["groupfinder-icon-class-hunter"]={29, 29, 0.328125, 0.356445, 0.0380859, 0.0664062, false, false},
		["groupfinder-icon-class-mage"]={29, 29, 0.328125, 0.356445, 0.0683594, 0.0966797, false, false},
		["groupfinder-icon-class-monk"]={29, 29, 0.701172, 0.729492, 0.0380859, 0.0664062, false, false},
		["groupfinder-icon-class-paladin"]={29, 29, 0.838867, 0.867188, 0.0986328, 0.126953, false, false},
		["groupfinder-icon-class-priest"]={29, 29, 0.701172, 0.729492, 0.0986328, 0.126953, false, false},
		["groupfinder-icon-class-rogue"]={29, 29, 0.916992, 0.945312, 0.*********, 0.0292969, false, false},
		["groupfinder-icon-class-shaman"]={29, 29, 0.286133, 0.314453, 0.*********, 0.0292969, false, false},
		["groupfinder-icon-class-warlock"]={29, 29, 0.886719, 0.915039, 0.*********, 0.0292969, false, false},
		["groupfinder-icon-class-warrior"]={29, 29, 0.947266, 0.975586, 0.*********, 0.0292969, false, false},
		["groupfinder-icon-emptyslot"]={29, 29, 0.869141, 0.897461, 0.0380859, 0.0664062, false, false},
		["groupfinder-icon-quest"]={14, 14, 0.977539, 0.991211, 0.015625, 0.0292969, false, false},
		["groupfinder-icon-role-large-dps"]={29, 29, 0.869141, 0.897461, 0.0683594, 0.0966797, false, false},
		["groupfinder-icon-role-large-heal"]={29, 29, 0.838867, 0.867188, 0.0380859, 0.0664062, false, false},
		["groupfinder-icon-role-large-tank"]={29, 29, 0.869141, 0.897461, 0.0986328, 0.126953, false, false},
		["groupfinder-button-ashran"]={290, 36, 0.*********, 0.28418, 0.643555, 0.678711, false, false},
		["groupfinder-background-arenas"]={333, 96, 0.*********, 0.326172, 0.0380859, 0.131836, false, false},
		["groupfinder-background-ashran"]={333, 96, 0.358398, 0.683594, 0.0380859, 0.131836, false, false},
		["groupfinder-background-battlegrounds"]={333, 96, 0.323242, 0.648438, 0.458008, 0.551758, false, false},
		["groupfinder-background-custom-pve"]={333, 96, 0.655273, 0.980469, 0.170898, 0.264648, false, false},
		["groupfinder-background-custom-pvp"]={333, 96, 0.323242, 0.648438, 0.266602, 0.360352, false, false},
		["groupfinder-background-dungeons"]={333, 96, 0.650391, 0.975586, 0.266602, 0.360352, false, false},
		["groupfinder-background-questing"]={333, 96, 0.323242, 0.648438, 0.362305, 0.456055, false, false},
		["groupfinder-background-raids-bc"]={333, 96, 0.650391, 0.975586, 0.362305, 0.456055, false, false},
		["groupfinder-background-raids-cataclysm"]={333, 96, 0.650391, 0.975586, 0.458008, 0.551758, false, false},
		["groupfinder-background-raids-classic"]={333, 96, 0.650391, 0.975586, 0.553711, 0.647461, false, false},
		["groupfinder-background-raids-mists"]={333, 96, 0.*********, 0.326172, 0.170898, 0.264648, false, false},
		["groupfinder-background-raids-wrath"]={333, 96, 0.323242, 0.648438, 0.649414, 0.743164, false, false},
		["groupfinder-background-ratedbgs"]={333, 96, 0.323242, 0.648438, 0.553711, 0.647461, false, false},
		["groupfinder-background-scenarios"]={333, 96, 0.650391, 0.975586, 0.649414, 0.743164, false, false},
		["groupfinder-background-skirmishes"]={333, 96, 0.650391, 0.975586, 0.745117, 0.838867, false, false},
		["groupfinder-background-raids-warlords"]={333, 96, 0.328125, 0.65332, 0.170898, 0.264648, false, false},
		["groupfinder-button-raids-warlords"]={290, 36, 0.*********, 0.28418, 0.*********, 0.0361328, false, false},
		["groupfinder-waitdot"]={19, 18, 0.731445, 0.75, 0.0585938, 0.0761719, false, false},
		["groupfinder-background-raids-legion"]={333, 96, 0.323242, 0.648438, 0.745117, 0.838867, false, false},
		["groupfinder-button-raids-legion"]={290, 36, 0.610352, 0.893555, 0.87793, 0.913086, false, false},
		["groupfinder-icon-class-demonhunter"]={29, 29, 0.701172, 0.729492, 0.0683594, 0.0966797, false, false},
	}, -- Interface/LFGFrame/GroupFinder
	["Interface/LFGFrame/LFG"]={
		["LFG-lock"]={17, 21, 0.46875, 0.734375, 0.03125, 0.6875, false, false},
		["DungeonTargetIndicator"]={27, 29, 0.015625, 0.4375, 0.03125, 0.9375, false, false},
	}, -- Interface/LFGFrame/LFG
	["Interface/LootFrame/LegendaryToast"]={
		["LegendaryToast-particles3"]={84, 163, 0.396484, 0.560547, 0.574219, 0.892578, false, false},
		["LegendaryToast-particles2"]={78, 147, 0.642578, 0.794922, 0.238281, 0.525391, false, false},
		["LegendaryToast-background"]={302, 119, 0.396484, 0.986328, 0.********, 0.234375, false, false},
		["LegendaryToast-ring1"]={131, 176, 0.********, 0.257812, 0.400391, 0.744141, false, false},
		["LegendaryToast-particles1"]={124, 170, 0.396484, 0.638672, 0.238281, 0.570312, false, false},
		["LegendaryToast-OrangeStarglow"]={200, 202, 0.********, 0.392578, 0.********, 0.396484, false, false},
	}, -- Interface/LootFrame/LegendaryToast
	["Interface/LootFrame/LootToastAtlas"]={
		["loottoast-arrow-blue"]={21, 25, 0.*********, 0.0214844, 0.835938, 0.933594, false, false},
		["loottoast-arrow-green"]={21, 25, 0.0234375, 0.0439453, 0.835938, 0.933594, false, false},
		["loottoast-arrow-orange"]={21, 25, 0.0458984, 0.0664062, 0.835938, 0.933594, false, false},
		["loottoast-arrow-purple"]={21, 25, 0.0683594, 0.0888672, 0.835938, 0.933594, false, false},
		["loottoast-bg-questrewardupgrade"]={276, 96, 0.555664, 0.825195, 0.********, 0.378906, false, false},
		["loottoast-glow"]={286, 109, 0.*********, 0.280273, 0.********, 0.429688, false, false},
		["loottoast-itemborder-blue"]={58, 58, 0.555664, 0.612305, 0.707031, 0.933594, false, false},
		["loottoast-itemborder-glow"]={68, 68, 0.827148, 0.893555, 0.386719, 0.652344, false, false},
		["loottoast-itemborder-green"]={58, 58, 0.614258, 0.670898, 0.707031, 0.933594, false, false},
		["loottoast-itemborder-orange"]={58, 58, 0.731445, 0.788086, 0.707031, 0.933594, false, false},
		["loottoast-itemborder-purple"]={58, 58, 0.790039, 0.84668, 0.707031, 0.933594, false, false},
		["loottoast-sheen"]={171, 75, 0.827148, 0.994141, 0.********, 0.296875, false, false},
		["loottoast-bg-alliance"]={278, 98, 0.282227, 0.553711, 0.********, 0.386719, false, false},
		["loottoast-bg-horde"]={282, 100, 0.*********, 0.276367, 0.4375, 0.828125, false, false},
		["LootToast-LessAwesome"]={276, 80, 0.555664, 0.825195, 0.386719, 0.699219, false, false},
		["LootToast-MoreAwesome"]={276, 96, 0.282227, 0.551758, 0.394531, 0.769531, false, false},
		["loottoast-itemborder-heirloom"]={58, 58, 0.672852, 0.729492, 0.707031, 0.933594, false, false},
		["loottoast-itemborder-artifact"]={58, 58, 0.895508, 0.952148, 0.386719, 0.613281, false, false},
		["loottoast-itemborder-gold"]={58, 58, 0.848633, 0.905273, 0.707031, 0.933594, false, false},
	}, -- Interface/LootFrame/LootToastAtlas
	["Interface/LootFrame/MountToast"]={
		["MountToast-Background"]={276, 109, 0.********, 0.541016, 0.0078125, 0.859375, false, false},
	}, -- Interface/LootFrame/MountToast
	["Interface/LootFrame/PetToast"]={
		["PetToast-background"]={276, 98, 0.********, 0.541016, 0.0078125, 0.773438, false, false},
	}, -- Interface/LootFrame/PetToast
	["Interface/LootFrame/PvPRatedLootToast"]={
		["pvprated-loottoast-bg-alliance"]={277, 113, 0.********, 0.542969, 0.460938, 0.902344, false, false},
		["pvprated-loottoast-bg-horde"]={281, 115, 0.********, 0.550781, 0.********, 0.453125, false, false},
	}, -- Interface/LootFrame/PvPRatedLootToast
	["Interface/LootFrame/RecipeToast"]={
		["recipetoast-bg"]={312, 89, 0.********, 0.611328, 0.0078125, 0.703125, false, false},
		["recipetoast-icon-star"]={20, 19, 0.********, 0.0410156, 0.71875, 0.867188, false, false},
	}, -- Interface/LootFrame/RecipeToast
	["Interface/MacroFrame/MacroPopup"]={
		["macropopup-bottomleft"]={18, 39, 0.160156, 0.230469, 0.597656, 0.75, false, false},
		["macropopup-bottomright"]={174, 39, 0.********, 0.683594, 0.4375, 0.589844, false, false},
		["macropopup-topleft"]={18, 71, 0.********, 0.0742188, 0.597656, 0.875, false, false},
		["macropopup-topright"]={18, 71, 0.0820312, 0.152344, 0.597656, 0.875, false, false},
		["_macropopup-bottom"]={256, 39, 0, 1, 0.277344, 0.429688, true, false},
		["_macropopup-top"]={256, 68, 0, 1, 0.********, 0.269531, true, false},
		["macropopup-scrollbar-bottom"]={30, 24, 0.816406, 0.933594, 0.4375, 0.53125, false, false},
		["macropopup-scrollbar-top"]={30, 25, 0.691406, 0.808594, 0.4375, 0.535156, false, false},
	}, -- Interface/MacroFrame/MacroPopup
	["Interface/MacroFrame/MacroPopupVertical"]={
		["!macropopup-left"]={17, 256, 0.257812, 0.390625, 0, 1, false, true},
		["!macropopup-right"]={17, 256, 0.40625, 0.539062, 0, 1, false, true},
		["!macropopup-scrollbar-middle"]={30, 256, 0.0078125, 0.242188, 0, 1, false, true},
	}, -- Interface/MacroFrame/MacroPopupVertical
	["Interface/Minimap/ArgusVindicaar"]={
		["FlightMaster_VindicaarArgus-TaxiNode_Neutral"]={91, 107, 0.207031, 0.384766, 0.********, 0.421875, false, false},
		["FlightMaster_VindicaarArgus-TaxiNode_Special"]={91, 107, 0.207031, 0.384766, 0.429688, 0.847656, false, false},
		["FlightMaster_VindicaarMacAree-TaxiNode_Neutral"]={85, 103, 0.388672, 0.554688, 0.********, 0.40625, false, false},
		["FlightMaster_VindicaarMacAree-TaxiNode_Special"]={85, 103, 0.388672, 0.554688, 0.414062, 0.816406, false, false},
		["FlightMaster_VindicaarStygianWake-TaxiNode_Neutral"]={103, 99, 0.********, 0.203125, 0.********, 0.390625, false, false},
		["FlightMaster_VindicaarStygianWake-TaxiNode_Special"]={103, 99, 0.********, 0.203125, 0.398438, 0.785156, false, false},
	}, -- Interface/Minimap/ArgusVindicaar
	["Interface/Minimap/MinimapClock"]={
		["UI-Minimap-Clock"]={55, 28, 0.015625, 0.875, 0.03125, 0.90625, false, false},
	}, -- Interface/Minimap/MinimapClock
	["Interface/Minimap/ObjectIconsAtlas"]={
		["WildBattlePet"]={32, 32, 0.402344, 0.464844, 0.859375, 0.921875, false, false},
		["StableMaster"]={32, 32, 0.269531, 0.332031, 0.859375, 0.921875, false, false},
		["ArchBlob"]={32, 32, 0.********, 0.0644531, 0.46875, 0.53125, false, false},
		["Banker"]={32, 32, 0.********, 0.0644531, 0.867188, 0.929688, false, false},
		["Focus"]={32, 32, 0.136719, 0.199219, 0.261719, 0.324219, false, false},
		["BattleMaster"]={32, 32, 0.********, 0.0644531, 0.933594, 0.996094, false, false},
		["Ammunition"]={32, 32, 0.********, 0.0644531, 0.335938, 0.398438, false, false},
		["Class"]={32, 32, 0.0703125, 0.132812, 0.261719, 0.324219, false, false},
		["Profession"]={32, 32, 0.203125, 0.265625, 0.925781, 0.988281, false, false},
		["Target"]={32, 32, 0.269531, 0.332031, 0.925781, 0.988281, false, false},
		["Food"]={32, 32, 0.136719, 0.199219, 0.394531, 0.457031, false, false},
		["Reagents"]={32, 32, 0.269531, 0.332031, 0.460938, 0.523438, false, false},
		["Innkeeper"]={32, 32, 0.136719, 0.199219, 0.59375, 0.65625, false, false},
		["Auctioneer"]={32, 32, 0.********, 0.0644531, 0.667969, 0.730469, false, false},
		["Repair"]={32, 32, 0.269531, 0.332031, 0.527344, 0.589844, false, false},
		["Mailbox"]={32, 32, 0.136719, 0.199219, 0.792969, 0.855469, false, false},
		["FlightMaster"]={32, 32, 0.0703125, 0.132812, 0.859375, 0.921875, false, false},
		["None"]={32, 32, 0.734375, 0.796875, 0.195312, 0.257812, false, false},
		["QuestBlob"]={32, 32, 0.402344, 0.464844, 0.261719, 0.324219, false, false},
		["TrivialQuests"]={32, 32, 0.402344, 0.464844, 0.328125, 0.390625, false, false},
		["Poisons"]={32, 32, 0.203125, 0.265625, 0.660156, 0.722656, false, false},
		["PlayerFriend"]={32, 32, 0.203125, 0.265625, 0.394531, 0.457031, false, false},
		["QuestRepeatableTurnin"]={32, 32, 0.867188, 0.929688, 0.261719, 0.324219, false, false},
		["MantidTowerDestroyed"]={32, 32, 0.136719, 0.199219, 0.925781, 0.988281, false, false},
		["PortalRed"]={32, 32, 0.203125, 0.265625, 0.859375, 0.921875, false, false},
		["QuestTurnin"]={32, 32, 0.269531, 0.332031, 0.328125, 0.390625, false, false},
		["Object"]={32, 32, 0.800781, 0.863281, 0.195312, 0.257812, false, false},
		["Gear"]={32, 32, 0.136719, 0.199219, 0.460938, 0.523438, false, false},
		["MonsterFriend"]={32, 32, 0.535156, 0.597656, 0.195312, 0.257812, false, false},
		["MonsterEnemy"]={32, 32, 0.46875, 0.53125, 0.195312, 0.257812, false, false},
		["PortalPurple"]={32, 32, 0.203125, 0.265625, 0.792969, 0.855469, false, false},
		["DungeonSkull"]={32, 32, 0.0703125, 0.132812, 0.792969, 0.855469, false, false},
		["PlayerNeutral"]={32, 32, 0.203125, 0.265625, 0.460938, 0.523438, false, false},
		["MonsterNeutral"]={32, 32, 0.601562, 0.664062, 0.195312, 0.257812, false, false},
		["PortalBlue"]={32, 32, 0.203125, 0.265625, 0.726562, 0.789062, false, false},
		["QuestLegendaryTurnin"]={32, 32, 0.667969, 0.730469, 0.261719, 0.324219, false, false},
		["QuestDaily"]={32, 32, 0.535156, 0.597656, 0.261719, 0.324219, false, false},
		["PlayerControlled"]={32, 32, 0.933594, 0.996094, 0.195312, 0.257812, false, false},
		["VignetteLoot"]={32, 32, 0.402344, 0.464844, 0.527344, 0.589844, false, false},
		["VignetteEvent"]={32, 32, 0.800781, 0.863281, 0.394531, 0.457031, false, false},
		["PlayerEnemy"]={32, 32, 0.203125, 0.265625, 0.328125, 0.390625, false, false},
		["PartyMember"]={32, 32, 0.867188, 0.929688, 0.195312, 0.257812, false, false},
		["XMarksTheSpot"]={32, 32, 0.535156, 0.597656, 0.460938, 0.523438, false, false},
		["QuestObjective"]={32, 32, 0.800781, 0.863281, 0.261719, 0.324219, false, false},
		["QuestLegendary"]={32, 32, 0.601562, 0.664062, 0.261719, 0.324219, false, false},
		["ArtifactQuest"]={32, 32, 0.********, 0.0644531, 0.535156, 0.597656, false, false},
		["QuestNormal"]={32, 32, 0.734375, 0.796875, 0.261719, 0.324219, false, false},
		["VignetteLootElite"]={32, 32, 0.402344, 0.464844, 0.59375, 0.65625, false, false},
		["FlightPath"]={32, 32, 0.136719, 0.199219, 0.195312, 0.257812, false, false},
		["VignetteEventElite"]={32, 32, 0.867188, 0.929688, 0.394531, 0.457031, false, false},
		["ChatBallon"]={32, 32, 0.0703125, 0.132812, 0.195312, 0.257812, false, false},
		["MantidTower"]={32, 32, 0.136719, 0.199219, 0.859375, 0.921875, false, false},
		["RaidMember"]={32, 32, 0.269531, 0.332031, 0.394531, 0.457031, false, false},
		["VignetteKill"]={32, 32, 0.933594, 0.996094, 0.394531, 0.457031, false, false},
		["QuestBonusObjective"]={32, 32, 0.46875, 0.53125, 0.261719, 0.324219, false, false},
		["ArtifactQuestTurnin"]={32, 32, 0.********, 0.0644531, 0.601562, 0.664062, false, false},
		["SmallQuestBang"]={32, 32, 0.269531, 0.332031, 0.792969, 0.855469, false, false},
		["VignetteKillElite"]={32, 32, 0.402344, 0.464844, 0.460938, 0.523438, false, false},
		["GreenCross"]={32, 32, 0.136719, 0.199219, 0.527344, 0.589844, false, false},
		["PlayerDeadBlip"]={32, 32, 0.203125, 0.265625, 0.261719, 0.324219, false, false},
		["PlayerPartyBlip"]={32, 32, 0.203125, 0.265625, 0.527344, 0.589844, false, false},
		["PlayerRaidBlip"]={32, 32, 0.203125, 0.265625, 0.59375, 0.65625, false, false},
		["Vehicle-Air-Alliance"]={64, 64, 0.130859, 0.255859, 0.********, 0.126953, false, false},
		["Vehicle-Air-Horde"]={64, 64, 0.259766, 0.384766, 0.********, 0.126953, false, false},
		["Vehicle-Air-Occupied"]={32, 32, 0.46875, 0.53125, 0.328125, 0.390625, false, false},
		["Vehicle-Air-Unoccupied"]={32, 32, 0.535156, 0.597656, 0.328125, 0.390625, false, false},
		["Vehicle-AllianceCart"]={32, 32, 0.601562, 0.664062, 0.328125, 0.390625, false, false},
		["Vehicle-Carriage"]={64, 64, 0.388672, 0.513672, 0.********, 0.126953, false, false},
		["Vehicle-Ground-Occupied"]={32, 32, 0.667969, 0.730469, 0.328125, 0.390625, false, false},
		["Vehicle-Ground-Unoccupied"]={32, 32, 0.734375, 0.796875, 0.328125, 0.390625, false, false},
		["Vehicle-GrummleConvoy"]={32, 32, 0.800781, 0.863281, 0.328125, 0.390625, false, false},
		["Vehicle-HammerGold-1"]={32, 32, 0.933594, 0.996094, 0.328125, 0.390625, false, false},
		["Vehicle-HammerGold-2"]={32, 32, 0.335938, 0.398438, 0.394531, 0.457031, false, false},
		["Vehicle-HammerGold-3"]={32, 32, 0.335938, 0.398438, 0.460938, 0.523438, false, false},
		["Vehicle-HammerGold"]={32, 32, 0.867188, 0.929688, 0.328125, 0.390625, false, false},
		["Vehicle-HordeCart"]={32, 32, 0.335938, 0.398438, 0.527344, 0.589844, false, false},
		["Vehicle-Mogu"]={32, 32, 0.335938, 0.398438, 0.59375, 0.65625, false, false},
		["Vehicle-SilvershardMines-Arrow"]={32, 32, 0.335938, 0.398438, 0.660156, 0.722656, false, false},
		["Vehicle-SilvershardMines-MineCart"]={32, 32, 0.335938, 0.398438, 0.726562, 0.789062, false, false},
		["Vehicle-SilvershardMines-MineCartBlue"]={32, 32, 0.335938, 0.398438, 0.792969, 0.855469, false, false},
		["Vehicle-SilvershardMines-MineCartRed"]={32, 32, 0.335938, 0.398438, 0.859375, 0.921875, false, false},
		["Vehicle-TempleofKotmogu-CyanBall"]={32, 32, 0.335938, 0.398438, 0.925781, 0.988281, false, false},
		["Vehicle-TempleofKotmogu-GreenBall"]={32, 32, 0.402344, 0.464844, 0.394531, 0.457031, false, false},
		["Vehicle-TempleofKotmogu-OrangeBall"]={32, 32, 0.46875, 0.53125, 0.394531, 0.457031, false, false},
		["Vehicle-TempleofKotmogu-PurpleBall"]={32, 32, 0.535156, 0.597656, 0.394531, 0.457031, false, false},
		["Vehicle-Trap-Gold"]={32, 32, 0.601562, 0.664062, 0.394531, 0.457031, false, false},
		["Vehicle-Trap-Grey"]={32, 32, 0.667969, 0.730469, 0.394531, 0.457031, false, false},
		["Vehicle-Trap-Red"]={32, 32, 0.734375, 0.796875, 0.394531, 0.457031, false, false},
		["QuestSkull"]={32, 32, 0.933594, 0.996094, 0.261719, 0.324219, false, false},
		["Focus-Tracker"]={32, 32, 0.136719, 0.199219, 0.328125, 0.390625, false, false},
		["MagePortalAlliance"]={32, 32, 0.136719, 0.199219, 0.660156, 0.722656, false, false},
		["MagePortalHorde"]={32, 32, 0.136719, 0.199219, 0.726562, 0.789062, false, false},
		["QuestArtifact"]={32, 32, 0.269531, 0.332031, 0.261719, 0.324219, false, false},
		["QuestArtifactTurnin"]={32, 32, 0.335938, 0.398438, 0.261719, 0.324219, false, false},
		["Target-Tracker"]={32, 32, 0.335938, 0.398438, 0.328125, 0.390625, false, false},
		["WarlockPortalAlliance"]={32, 32, 0.402344, 0.464844, 0.726562, 0.789062, false, false},
		["WarlockPortalHorde"]={32, 32, 0.402344, 0.464844, 0.792969, 0.855469, false, false},
		["WildBattlePet-Tracker"]={32, 32, 0.402344, 0.464844, 0.925781, 0.988281, false, false},
		["WildBattlePetCapturable"]={32, 32, 0.46875, 0.53125, 0.460938, 0.523438, false, false},
		["CrossedFlags"]={32, 32, 0.0703125, 0.132812, 0.328125, 0.390625, false, false},
		["CrossedFlagsWithTimer"]={32, 32, 0.0703125, 0.132812, 0.394531, 0.457031, false, false},
		["MiniMap-DeadArrow"]={32, 32, 0.203125, 0.265625, 0.195312, 0.257812, false, false},
		["MiniMap-PositionArrows"]={16, 32, 0.46875, 0.5, 0.660156, 0.722656, false, false},
		["MiniMap-QuestArrow"]={32, 32, 0.269531, 0.332031, 0.195312, 0.257812, false, false},
		["MiniMap-VignetteArrow"]={32, 32, 0.335938, 0.398438, 0.195312, 0.257812, false, false},
		["MinimapArrow"]={32, 32, 0.402344, 0.464844, 0.195312, 0.257812, false, false},
		["Rotating-MinimapArrow"]={32, 32, 0.269531, 0.332031, 0.59375, 0.65625, false, false},
		["Rotating-MinimapGroupArrow"]={32, 32, 0.269531, 0.332031, 0.660156, 0.722656, false, false},
		["Rotating-MinimapGuideArrow"]={32, 32, 0.269531, 0.332031, 0.726562, 0.789062, false, false},
		["MiniMap-PositionArrowDown"]={48, 48, 0.646484, 0.740234, 0.********, 0.0957031, false, false},
		["MiniMap-PositionArrowUp"]={48, 48, 0.744141, 0.837891, 0.********, 0.0957031, false, false},
		["MovieRecordingIcon"]={32, 32, 0.667969, 0.730469, 0.195312, 0.257812, false, false},
		["DemonInvasion1"]={32, 32, 0.0703125, 0.132812, 0.460938, 0.523438, false, false},
		["DemonInvasion2"]={32, 32, 0.0703125, 0.132812, 0.527344, 0.589844, false, false},
		["DemonInvasion3"]={32, 32, 0.0703125, 0.132812, 0.59375, 0.65625, false, false},
		["DemonInvasion4"]={32, 32, 0.0703125, 0.132812, 0.660156, 0.722656, false, false},
		["DemonInvasion5"]={32, 32, 0.0703125, 0.132812, 0.726562, 0.789062, false, false},
		["poi-alliance"]={32, 32, 0.800781, 0.863281, 0.460938, 0.523438, false, false},
		["poi-horde"]={32, 32, 0.867188, 0.929688, 0.460938, 0.523438, false, false},
		["poi-workorders"]={32, 32, 0.46875, 0.53125, 0.59375, 0.65625, false, false},
		["poi-majorcity"]={32, 32, 0.933594, 0.996094, 0.460938, 0.523438, false, false},
		["poi-town"]={32, 32, 0.46875, 0.53125, 0.527344, 0.589844, false, false},
		["map-icon-ignored-blueexclaimation"]={32, 32, 0.667969, 0.730469, 0.460938, 0.523438, false, false},
		["map-icon-ignored-bluequestion"]={32, 32, 0.734375, 0.796875, 0.460938, 0.523438, false, false},
		["map-icon-deathknightclasshall"]={64, 64, 0.517578, 0.642578, 0.********, 0.126953, false, false},
		["Dungeon"]={22, 22, 0.378906, 0.421875, 0.130859, 0.173828, false, false},
		["Raid"]={22, 22, 0.425781, 0.46875, 0.130859, 0.173828, false, false},
		["map-icon-SuramarDoor.tga"]={32, 32, 0.601562, 0.664062, 0.460938, 0.523438, false, false},
		["AncientMana"]={32, 32, 0.********, 0.0644531, 0.402344, 0.464844, false, false},
		["LegionfallMapBanner"]={64, 64, 0.********, 0.126953, 0.********, 0.126953, false, false},
		["poi-transmogrifier"]={29, 36, 0.********, 0.0585938, 0.261719, 0.332031, false, false},
		["DemonShip"]={31, 56, 0.841797, 0.902344, 0.********, 0.111328, false, false},
		["DemonShip_East"]={56, 31, 0.********, 0.111328, 0.130859, 0.191406, false, false},
		["poi-graveyard-neutral"]={12, 16, 0.550781, 0.574219, 0.130859, 0.162109, false, false},
		["poi-door-arrow-down"]={13, 14, 0.646484, 0.671875, 0.0996094, 0.126953, false, false},
		["poi-door-arrow-up"]={13, 14, 0.675781, 0.701172, 0.0996094, 0.126953, false, false},
		["poi-door"]={25, 24, 0.115234, 0.164062, 0.130859, 0.177734, false, false},
		["TaxiNode_Alliance"]={18, 18, 0.962891, 0.998047, 0.0761719, 0.111328, false, false},
		["TaxiNode_Horde"]={18, 18, 0.472656, 0.507812, 0.130859, 0.166016, false, false},
		["TaxiNode_Neutral"]={18, 18, 0.511719, 0.546875, 0.130859, 0.166016, false, false},
		["FlightMaster_Argus-TaxiNode_Neutral"]={42, 36, 0.90625, 0.988281, 0.********, 0.0722656, false, false},
		["poi-rift1"]={27, 26, 0.90625, 0.958984, 0.0761719, 0.126953, false, false},
		["poi-rift2"]={33, 32, 0.********, 0.0664062, 0.195312, 0.257812, false, false},
		["FlightMasterArgus"]={32, 32, 0.0703125, 0.132812, 0.925781, 0.988281, false, false},
		["poi-door-down"]={25, 24, 0.167969, 0.216797, 0.130859, 0.177734, false, false},
		["poi-door-left"]={25, 24, 0.220703, 0.269531, 0.130859, 0.177734, false, false},
		["poi-door-right"]={25, 24, 0.273438, 0.322266, 0.130859, 0.177734, false, false},
		["poi-door-up"]={25, 24, 0.326172, 0.375, 0.130859, 0.177734, false, false},
		["AzeriteReady"]={32, 32, 0.********, 0.0644531, 0.734375, 0.796875, false, false},
		["AzeriteSpawning"]={32, 32, 0.********, 0.0644531, 0.800781, 0.863281, false, false},
		["Warboard"]={32, 32, 0.402344, 0.464844, 0.660156, 0.722656, false, false},
	}, -- Interface/Minimap/ObjectIconsAtlas
	["Interface/Minimap/PartyRaidBlipsV2"]={
		["WhiteCircle-RaidBlips"]={19, 18, 0.015625, 0.3125, 0.03125, 0.59375, false, false},
		["WhiteDotCircle-RaidBlips"]={19, 18, 0.34375, 0.640625, 0.03125, 0.59375, false, false},
	}, -- Interface/Minimap/PartyRaidBlipsV2
	["Interface/MoneyFrame/MoneyFrame"]={
		["UI-MoneyFrame-Large"]={172, 120, 0.********, 0.675781, 0.0078125, 0.945312, false, false},
	}, -- Interface/MoneyFrame/MoneyFrame
	["Interface/OptionsFrame/ColorblindSettings"]={
		["colorblind-bar-green"]={108, 17, 0.********, 0.425781, 0.589844, 0.65625, false, false},
		["colorblind-bar-red"]={108, 17, 0.433594, 0.855469, 0.589844, 0.65625, false, false},
		["colorblind-bar-yellow"]={108, 17, 0.********, 0.425781, 0.664062, 0.730469, false, false},
		["colorblind-colorwheel"]={148, 148, 0.********, 0.582031, 0.********, 0.582031, false, false},
	}, -- Interface/OptionsFrame/ColorblindSettings
	["Interface/OptionsFrame/Options"]={
		["options-notch"]={1, 6, 0.25, 0.5, 0.125, 0.875, false, false},
	}, -- Interface/OptionsFrame/Options
	["Interface/PaperDoll/Inspect"]={
		["inspect-talent-selected"]={38, 38, 0.015625, 0.609375, 0.015625, 0.609375, false, false},
	}, -- Interface/PaperDoll/Inspect
	["Interface/PaperDollInfoFrame/PaperDollInfoPart1"]={
		["UI-Character-Info-Paladin-BG"]={197, 355, 0.195312, 0.387695, 0.*********, 0.347656, false, false},
		["UI-Character-Info-Priest-BG"]={197, 355, 0.195312, 0.387695, 0.349609, 0.696289, false, false},
		["UI-Character-Info-Warlock-BG"]={197, 355, 0.583984, 0.776367, 0.*********, 0.347656, false, false},
		["UI-Character-Info-Line-Bounce"]={157, 19, 0.*********, 0.154297, 0.769531, 0.788086, false, false},
		["UI-Character-Info-Shaman-BG"]={197, 355, 0.389648, 0.582031, 0.349609, 0.696289, false, false},
		["UI-Character-Info-ItemLevel-Bounce"]={162, 29, 0.*********, 0.15918, 0.739258, 0.767578, false, false},
		["UI-Character-Info-Warrior-BG"]={197, 355, 0.77832, 0.970703, 0.*********, 0.347656, false, false},
		["UI-Character-Info-Title"]={196, 40, 0.*********, 0.192383, 0.698242, 0.737305, false, false},
		["UI-Character-Info-Mage-BG"]={197, 355, 0.*********, 0.193359, 0.*********, 0.347656, false, false},
		["UI-Character-Info-Rogue-BG"]={197, 355, 0.389648, 0.582031, 0.*********, 0.347656, false, false},
		["UI-Character-Info-Monk-BG"]={197, 355, 0.*********, 0.193359, 0.349609, 0.696289, false, false},
	}, -- Interface/PaperDollInfoFrame/PaperDollInfoPart1
	["Interface/PaperDollInfoFrame/PaperDollInfoPart2"]={
		["UI-Character-Info-DeathKnight-BG"]={197, 355, 0.*********, 0.193359, 0.********, 0.695312, false, false},
		["UI-Character-Info-DemonHunter-BG"]={197, 355, 0.195312, 0.387695, 0.********, 0.695312, false, false},
		["UI-Character-Info-Druid-BG"]={197, 355, 0.389648, 0.582031, 0.********, 0.695312, false, false},
		["UI-Character-Info-Hunter-BG"]={197, 355, 0.583984, 0.776367, 0.********, 0.695312, false, false},
	}, -- Interface/PaperDollInfoFrame/PaperDollInfoPart2
	["Interface/PaperDollInfoFrame/ParagonReputation"]={
		["ParagonReputation_Bag"]={15, 18, 0.59375, 0.828125, 0.015625, 0.296875, false, false},
		["ParagonReputation_Checkmark"]={16, 14, 0.59375, 0.84375, 0.328125, 0.546875, false, false},
		["ParagonReputation_Glow"]={35, 35, 0.015625, 0.5625, 0.015625, 0.5625, false, false},
	}, -- Interface/PaperDollInfoFrame/ParagonReputation
	["Interface/PetBattles/MountJournalicons"]={
		["MountJournalIcons-Alliance"]={46, 44, 0.0078125, 0.367188, 0.015625, 0.703125, false, false},
		["MountJournalIcons-Horde"]={46, 44, 0.382812, 0.742188, 0.015625, 0.703125, false, false},
	}, -- Interface/PetBattles/MountJournalicons
	["Interface/PetBattles/PetBattleHUDAtlas"]={
		["MainPet-PetFamilyFrame"]={34, 34, 0.860352, 0.893555, 0.963867, 0.99707, false, false},
		["Timer-Frame"]={339, 17, 0.508789, 0.839844, 0.655273, 0.671875, false, false},
		["MainPet-HealthBarBG"]={155, 47, 0.725586, 0.876953, 0.749023, 0.794922, false, false},
		["Timer-BG"]={473, 27, 0.508789, 0.970703, 0.628906, 0.655273, false, false},
		["BackupPet-DeadFrame"]={47, 47, 0.920898, 0.966797, 0.833008, 0.878906, false, false},
		["MainPet-HealthBarFrame"]={155, 47, 0.211914, 0.363281, 0.859375, 0.905273, false, false},
		["Start-VersusSplash"]={217, 212, 0, 0.211914, 0.628906, 0.835938, false, false},
		["BackupPet-Frame"]={47, 47, 0.927734, 0.973633, 0.947266, 0.993164, false, false},
		["BattleHUD-Top"]={574, 118, 0, 0.560547, 0.385742, 0.500977, false, false},
		["BattleBar-ButtonBG-EndCap"]={24, 78, 0.0263672, 0.0498047, 0.835938, 0.912109, false, false},
		["BattleBar-SwapPetFrame-DeadIcon"]={33, 33, 0.893555, 0.925781, 0.963867, 0.996094, false, false},
		["MainPet-HealthBarFill"]={145, 37, 0.396484, 0.538086, 0.891602, 0.927734, false, false},
		["BattleBar-EndCap"]={127, 125, 0.538086, 0.662109, 0.833008, 0.955078, false, false},
		["BattleBar-Button-Highlight"]={93, 93, 0.769531, 0.860352, 0.896484, 0.987305, false, false},
		["BattleBar-SwapPetFrame"]={192, 86, 0.538086, 0.725586, 0.749023, 0.833008, false, false},
		["Timer-Fill"]={339, 17, 0.508789, 0.839844, 0.671875, 0.688477, false, false},
		["BattleBar-SwapPetIcon"]={44, 42, 0.707031, 0.75, 0.939453, 0.980469, false, false},
		["MainPet-Frame"]={110, 109, 0.662109, 0.769531, 0.833008, 0.939453, false, false},
		["BattleBar-Countdown-Shadow"]={53, 52, 0.927734, 0.979492, 0.896484, 0.947266, false, false},
		["BattleBar-ButtonBG-Divider"]={27, 81, 0, 0.0263672, 0.835938, 0.915039, false, false},
		["BattleBar-SwapPetShadow"]={145, 146, 0.396484, 0.538086, 0.749023, 0.891602, false, false},
		["BattleBar-SwapPetFrame-Highlight"]={169, 42, 0.211914, 0.376953, 0.905273, 0.946289, false, false},
		["MainPet-PetFamilyActivate"]={46, 46, 0.662109, 0.707031, 0.939453, 0.984375, false, false},
		["MainPet-LevelBubble"]={24, 24, 0.973633, 0.99707, 0.947266, 0.970703, false, false},
		["BattleHUD-Versus"]={155, 65, 0.769531, 0.920898, 0.833008, 0.896484, false, false},
		["_BattleBar-ButtonBGMid"]={256, 78, 0, 0.25, 0.121094, 0.197266, true, false},
		["_BattleBar-Mid"]={256, 124, 0, 0.25, 0, 0.121094, true, false},
	}, -- Interface/PetBattles/PetBattleHUDAtlas
	["Interface/PetBattles/PetJournalBigChunks"]={
		["PetJournal-BattleSlot-Active"]={404, 106, 0.********, 0.791016, 0.339844, 0.546875, false, false},
		["PetJournal-BattleSlot-Locked"]={404, 106, 0.********, 0.791016, 0.550781, 0.757812, false, false},
		["PetJournal-PetCard-BG"]={408, 171, 0.********, 0.798828, 0.********, 0.335938, false, false},
	}, -- Interface/PetBattles/PetJournalBigChunks
	["Interface/PetBattles/PetJournalBits"]={
		["PetJournal-BattleSlotFrame-Corner"]={17, 17, 0.929688, 0.996094, 0.191406, 0.224609, false, false},
		["PetJournal-ExpBar-Left"]={11, 11, 0.929688, 0.972656, 0.253906, 0.275391, false, false},
		["PetJournal-BattleSlotTitle-Right"]={40, 40, 0.********, 0.160156, 0.644531, 0.722656, false, false},
		["PetJournal-ExpBar-Background"]={11, 11, 0.929688, 0.972656, 0.228516, 0.25, false, false},
		["PetJournal-PetBattleAchievementGlow"]={235, 49, 0.********, 0.921875, 0.191406, 0.287109, false, false},
		["PetJournal-HealthBar-Left"]={11, 7, 0.652344, 0.695312, 0.417969, 0.431641, false, false},
		["PetJournal-PetCard-Abilities"]={57, 105, 0.********, 0.226562, 0.435547, 0.640625, false, false},
		["PetJournal-BattleSlot-AbilityBorder"]={33, 33, 0.********, 0.132812, 0.726562, 0.791016, false, false},
		["PetJournal-ExpBar-Divider"]={7, 7, 0.929688, 0.957031, 0.291016, 0.304688, false, false},
		["PetJournal-ExpBar-Right"]={11, 11, 0.601562, 0.644531, 0.400391, 0.421875, false, false},
		["PetJournal-BattleSlot-IconBorder"]={53, 54, 0.550781, 0.757812, 0.291016, 0.396484, false, false},
		["PetJournal-LevelBubble"]={21, 21, 0.140625, 0.222656, 0.726562, 0.767578, false, false},
		["PetJournal-ExpBar-Mid"]={11, 11, 0.550781, 0.59375, 0.400391, 0.421875, false, false},
		["PetJournal-PetBattleAchievementBG"]={50, 22, 0.765625, 0.960938, 0.373047, 0.416016, false, false},
		["PetJournal-BattleSlot-Shadow"]={138, 72, 0.********, 0.542969, 0.291016, 0.431641, false, false},
		["PetJournal-BattleSlotTitle-Left"]={40, 40, 0.765625, 0.921875, 0.291016, 0.369141, false, false},
		["PetJournal-HealthBar-Mid"]={11, 7, 0.703125, 0.746094, 0.400391, 0.414062, false, false},
		["PetJournal-HealthBar-Right"]={11, 7, 0.703125, 0.746094, 0.417969, 0.431641, false, false},
		["PetJournal-HealthBar-Background"]={11, 7, 0.652344, 0.695312, 0.400391, 0.414062, false, false},
		["_BattleSlotTitle-BG"]={64, 24, 0, 0.25, 0.0839844, 0.130859, true, false},
		["_BattleSlotFrame-Top"]={64, 16, 0, 0.25, 0.134766, 0.166016, true, false},
		["_BattleSlotFrame-Divider"]={64, 9, 0, 0.25, 0.169922, 0.1875, true, false},
		["_BattleSlotTitle-Mid"]={64, 40, 0, 0.25, 0.********, 0.0800781, true, false},
	}, -- Interface/PetBattles/PetJournalBits
	["Interface/PlayerFrame/ClassOverlayComboPoints"]={
		["ClassOverlay-ComboPoint"]={20, 20, 0.78125, 0.9375, 0.328125, 0.640625, false, false},
		["ClassOverlay-ComboPoint-Off"]={20, 20, 0.78125, 0.9375, 0.671875, 0.984375, false, false},
		["ComboPoints-AllPointsBG"]={126, 18, 0.0078125, 0.992188, 0.015625, 0.296875, false, false},
		["ComboPoints-PointBg"]={20, 21, 0.609375, 0.765625, 0.328125, 0.65625, false, false},
		["ComboPoints-ComboPointDash"]={9, 10, 0.226562, 0.296875, 0.75, 0.90625, false, false},
		["ComboPoints-ComboPointDash-Bg"]={9, 10, 0.3125, 0.382812, 0.75, 0.90625, false, false},
		["ComboPoints-ComboPoint"]={20, 21, 0.4375, 0.59375, 0.328125, 0.65625, false, false},
		["ComboPoints-FX-Circle"]={26, 27, 0.0078125, 0.210938, 0.328125, 0.75, false, false},
		["ComboPoints-FX-Dash"]={25, 8, 0.0078125, 0.203125, 0.78125, 0.90625, false, false},
		["ComboPoints-FX-Star"]={25, 25, 0.226562, 0.421875, 0.328125, 0.71875, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayComboPoints
	["Interface/PlayerFrame/ClassOverlayDeathKnight"]={
		["ClassOverlay-Rune"]={25, 28, 0.********, 0.101562, 0.03125, 0.90625, false, false},
		["ClassOverlay-RunicPower"]={142, 6, 0.109375, 0.664062, 0.03125, 0.21875, false, false},
		["ClassOverlay-RunicPowerBg"]={142, 6, 0.109375, 0.664062, 0.28125, 0.46875, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayDeathKnight
	["Interface/PlayerFrame/ClassOverlayDeathKnightRunes"]={
		["DK-Blood-Rune-Ready"]={32, 32, 0.0078125, 0.257812, 0.0078125, 0.257812, false, false},
		["DK-Rune-CD"]={32, 32, 0.539062, 0.789062, 0.0078125, 0.257812, false, false},
		["DK-Rune-Glow"]={32, 32, 0.0078125, 0.257812, 0.273438, 0.523438, false, false},
		["DK-Unholy-Rune-Ready"]={32, 32, 0.0078125, 0.257812, 0.539062, 0.789062, false, false},
		["DK-Frost-Rune-Ready"]={32, 32, 0.273438, 0.523438, 0.0078125, 0.257812, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayDeathKnightRunes
	["Interface/PlayerFrame/ClassOverlayDeathKnightRunesFill"]={
		["DK-Blood-Rune-CDFill"]={34, 37, 0.0078125, 0.273438, 0.0078125, 0.296875, false, false},
		["DK-BloodUnholy-Rune-CDSpark"]={34, 37, 0.0078125, 0.273438, 0.3125, 0.601562, false, false},
		["DK-Frost-Rune-CDFill"]={34, 37, 0.0078125, 0.273438, 0.617188, 0.90625, false, false},
		["DK-Frost-Rune-CDSpark"]={34, 37, 0.289062, 0.554688, 0.0078125, 0.296875, false, false},
		["DK-Unholy-Rune-CDFill"]={34, 37, 0.570312, 0.835938, 0.0078125, 0.296875, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayDeathKnightRunesFill
	["Interface/PlayerFrame/ClassOverlayFury"]={
		["ClassOverlay-Fury"]={118, 11, 0.0078125, 0.929688, 0.03125, 0.375, false, false},
		["ClassOverlay-FuryBG"]={118, 11, 0.0078125, 0.929688, 0.4375, 0.78125, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayFury
	["Interface/PlayerFrame/ClassOverlayHolyPower"]={
		["ClassOverlay-HolyPower1off"]={32, 22, 0.269531, 0.394531, 0.5625, 0.90625, false, false},
		["ClassOverlay-HolyPower1on"]={32, 22, 0.53125, 0.65625, 0.015625, 0.359375, false, false},
		["ClassOverlay-HolyPower2off"]={33, 20, 0.664062, 0.792969, 0.015625, 0.328125, false, false},
		["ClassOverlay-HolyPower2on"]={33, 20, 0.800781, 0.929688, 0.015625, 0.328125, false, false},
		["ClassOverlay-HolyPower3off"]={32, 24, 0.********, 0.128906, 0.5625, 0.9375, false, false},
		["ClassOverlay-HolyPower3on"]={32, 24, 0.136719, 0.261719, 0.5625, 0.9375, false, false},
		["ClassOverlay-HolyPower4off"]={33, 17, 0.664062, 0.792969, 0.359375, 0.625, false, false},
		["ClassOverlay-HolyPower4on"]={33, 17, 0.664062, 0.792969, 0.65625, 0.921875, false, false},
		["ClassOverlay-HolyPower5off"]={33, 17, 0.800781, 0.929688, 0.359375, 0.625, false, false},
		["ClassOverlay-HolyPower5on"]={33, 17, 0.800781, 0.929688, 0.65625, 0.921875, false, false},
		["ClassOverlay-HolyPowerBG"]={133, 33, 0.********, 0.523438, 0.015625, 0.53125, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayHolyPower
	["Interface/PlayerFrame/ClassOverlayWarlockShards"]={
		["nameplate-WarlockShard-Off"]={17, 17, 0.03125, 0.5625, 0.40625, 0.671875, false, false},
		["nameplate-WarlockShard-On"]={17, 17, 0.03125, 0.5625, 0.703125, 0.96875, false, false},
		["nameplate-WarlockShard-Glow"]={26, 23, 0.03125, 0.84375, 0.015625, 0.375, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayWarlockShards
	["Interface/PlayerFrame/ClassOverlayWarlockShards725"]={
		["Warlock-Bar-EmptyShards"]={133, 29, 0.164062, 0.683594, 0.09375, 0.546875, false, false},
		["Warlock-EmptyShard"]={17, 22, 0.********, 0.0703125, 0.59375, 0.9375, false, false},
		["Warlock-FillShard"]={17, 22, 0.0898438, 0.15625, 0.015625, 0.359375, false, false},
		["Warlock-ReadyShard-Glow"]={20, 35, 0.********, 0.0820312, 0.015625, 0.5625, false, false},
		["Warlock-ReadyShard"]={17, 22, 0.0898438, 0.15625, 0.390625, 0.734375, false, false},
		["Warlock-Shard-Spark"]={21, 3, 0.164062, 0.246094, 0.015625, 0.0625, false, false},
	}, -- Interface/PlayerFrame/ClassOverlayWarlockShards725
	["Interface/PlayerFrame/DemonHunterBarHorizontal"]={
		["_DemonHunter-DemonicFuryBar"]={128, 10, 0, 1, 0.03125, 0.34375, true, false},
		["_DemonHunter-DemonicPainBar"]={128, 10, 0, 1, 0.40625, 0.71875, true, false},
	}, -- Interface/PlayerFrame/DemonHunterBarHorizontal
	["Interface/PlayerFrame/DruidEclipse"]={
		["DruidEclipse-LunarCover"]={60, 35, 0.********, 0.238281, 0.59375, 0.867188, false, false},
		["DruidEclipse-SolarCover"]={74, 32, 0.4375, 0.726562, 0.3125, 0.5625, false, false},
		["DruidEclipse-Arrow"]={24, 24, 0.734375, 0.828125, 0.3125, 0.5, false, false},
		["DruidEclipse-BaseBar"]={140, 37, 0.********, 0.550781, 0.0078125, 0.296875, false, false},
		["DruidEclipse-BaseMoon"]={23, 23, 0.835938, 0.925781, 0.3125, 0.492188, false, false},
		["DruidEclipse-BaseSun"]={23, 23, 0.59375, 0.683594, 0.59375, 0.773438, false, false},
		["DruidEclipse-Diamond"]={24, 22, 0.789062, 0.882812, 0.59375, 0.765625, false, false},
		["DruidEclipse-Line"]={12, 22, 0.933594, 0.980469, 0.3125, 0.484375, false, false},
		["DruidEclipse-LunarBar"]={110, 37, 0.558594, 0.988281, 0.0078125, 0.296875, false, false},
		["DruidEclipse-LunarMoon"]={42, 45, 0.421875, 0.585938, 0.59375, 0.945312, false, false},
		["DruidEclipse-LunarSun"]={23, 23, 0.59375, 0.683594, 0.789062, 0.96875, false, false},
		["DruidEclipse-SolarBar"]={109, 34, 0.********, 0.429688, 0.3125, 0.578125, false, false},
		["DruidEclipse-SolarMoon"]={23, 23, 0.691406, 0.78125, 0.59375, 0.773438, false, false},
		["DruidEclipse-SolarSun"]={43, 45, 0.246094, 0.414062, 0.59375, 0.945312, false, false},
	}, -- Interface/PlayerFrame/DruidEclipse
	["Interface/PlayerFrame/DruidLunarBarHorizontal"]={
		["_Druid-LunarBar"]={128, 10, 0, 1, 0.0625, 0.6875, true, false},
	}, -- Interface/PlayerFrame/DruidLunarBarHorizontal
	["Interface/PlayerFrame/MageArcaneCharges"]={
		["Mage-ArcaneCharge"]={39, 39, 0.234375, 0.386719, 0.476562, 0.78125, false, false},
		["Mage-ArcaneChargeBar"]={125, 15, 0.234375, 0.722656, 0.0078125, 0.125, false, false},
		["Mage-ArcaneCharge-CircleGlow"]={41, 41, 0.234375, 0.394531, 0.140625, 0.460938, false, false},
		["Mage-ArcaneCharge-Rune"]={46, 45, 0.********, 0.183594, 0.585938, 0.9375, false, false},
		["Mage-ArcaneCharge-SmallSpark"]={38, 39, 0.402344, 0.550781, 0.140625, 0.445312, false, false},
		["Mage-ArcaneCharge-Spark"]={57, 72, 0.********, 0.226562, 0.0078125, 0.570312, false, false},
	}, -- Interface/PlayerFrame/MageArcaneCharges
	["Interface/PlayerFrame/MonkUIAtlas"]={
		["MonkUI-background-shadow"]={136, 43, 0.********, 0.535156, 0.0078125, 0.34375, false, false},
		["MonkUI-OrbOff"]={21, 21, 0.542969, 0.625, 0.0078125, 0.171875, false, false},
		["MonkUI-OrbOff-small"]={18, 18, 0.542969, 0.613281, 0.1875, 0.328125, false, false},
		["MonkUI-LightOrb-small"]={18, 18, 0.632812, 0.703125, 0.1875, 0.328125, false, false},
		["MonkUI-background"]={136, 43, 0.********, 0.535156, 0.359375, 0.695312, false, false},
		["MonkUI-LightOrb"]={21, 21, 0.632812, 0.714844, 0.0078125, 0.171875, false, false},
	}, -- Interface/PlayerFrame/MonkUIAtlas
	["Interface/PlayerFrame/PlayerFrameFX"]={
		["FullAlert-BigSpike"]={27, 34, 0.0078125, 0.21875, 0.015625, 0.546875, false, false},
		["FullAlert-SoftCurveGlow"]={20, 37, 0.234375, 0.390625, 0.015625, 0.59375, false, false},
		["FullAlert-YellowCurveGlow"]={20, 37, 0.40625, 0.5625, 0.015625, 0.59375, false, false},
		["FullAlert-FrameGlow"]={34, 18, 0.578125, 0.84375, 0.015625, 0.296875, false, false},
	}, -- Interface/PlayerFrame/PlayerFrameFX
	["Interface/PlayerFrame/PriestInsanityBar"]={
		["Priest-InsanityOverlay"]={55, 36, 0.765625, 0.980469, 0.0507812, 0.191406, false, false},
		["_Priest-InsanityBar"]={128, 10, 0, 0.5, 0.********, 0.0429688, true, false},
		["Insanity-DrippyPurple1"]={49, 33, 0.765625, 0.957031, 0.199219, 0.328125, false, false},
		["Insanity-DrippyPurple2"]={49, 29, 0.515625, 0.707031, 0.683594, 0.796875, false, false},
		["Insanity-PortraitOverlay"]={193, 77, 0.********, 0.757812, 0.0507812, 0.351562, false, false},
		["Insanity-PurpleBurstOn"]={174, 27, 0.********, 0.683594, 0.570312, 0.675781, false, false},
		["Insanity-ShadowBurstOn"]={139, 34, 0.417969, 0.960938, 0.359375, 0.492188, false, false},
		["Insanity-Tentacles"]={129, 30, 0.********, 0.507812, 0.683594, 0.800781, false, false},
		["Insanity-TopPurpleShadow"]={104, 52, 0.********, 0.410156, 0.359375, 0.5625, false, false},
		["Insanity-Spark"]={9, 41, 0.********, 0.0390625, 0.808594, 0.96875, false, false},
	}, -- Interface/PlayerFrame/PriestInsanityBar
	["Interface/PlayerFrame/PriestShadowUI"]={
		["shadoworbs-small-Orb-Bg"]={28, 28, 0.********, 0.113281, 0.558594, 0.667969, false, false},
		["shadoworbs-small-Orb"]={25, 25, 0.********, 0.101562, 0.675781, 0.773438, false, false},
		["shadoworbs-small-Frame-OrbHighlight"]={52, 29, 0.632812, 0.835938, 0.222656, 0.335938, false, false},
		["shadoworbs-large-Orb"]={38, 37, 0.84375, 0.992188, 0.222656, 0.367188, false, false},
		["shadoworbs-large-Frame"]={159, 54, 0.********, 0.625, 0.********, 0.214844, false, false},
		["shadoworbs-large-Orb-Bg"]={38, 37, 0.********, 0.152344, 0.40625, 0.550781, false, false},
		["shadoworbs-large-Frame-OrbHighlight"]={74, 44, 0.632812, 0.921875, 0.********, 0.175781, false, false},
		["shadoworbs-small-Frame"]={159, 45, 0.********, 0.625, 0.222656, 0.398438, false, false},
	}, -- Interface/PlayerFrame/PriestShadowUI
	["Interface/PlayerFrame/ShamanMaelstromBarHorizontal"]={
		["_Shaman-MaelstromBar"]={128, 10, 0, 1, 0.0625, 0.6875, true, false},
	}, -- Interface/PlayerFrame/ShamanMaelstromBarHorizontal
	["Interface/PVPFrame/PvPHonorSystem"]={
		["honorsystem-bar-background"]={471, 18, 0.126953, 0.586914, 0.422852, 0.44043, false, false},
		["honorsystem-bar-frame"]={521, 45, 0.*********, 0.509766, 0.376953, 0.420898, false, false},
		["honorsystem-bar-spark"]={16, 64, 0.888672, 0.904297, 0.217773, 0.280273, false, false},
		["honorsystem-bar-rewardborder-prestige-flash"]={127, 131, 0.*********, 0.125, 0.422852, 0.550781, false, false},
		["honorsystem-bar-rewardborder-prestige"]={59, 60, 0.880859, 0.938477, 0.0546875, 0.113281, false, false},
		["honorsystem-bar-rewardborder"]={39, 39, 0.917969, 0.956055, 0.115234, 0.15332, false, false},
		["honorsystem-prestige-laurel-bg-alliance"]={134, 134, 0.623047, 0.753906, 0.217773, 0.348633, false, false},
		["honorsystem-prestige-laurel-bg-horde"]={134, 134, 0.755859, 0.886719, 0.217773, 0.348633, false, false},
		["honorsystem-prestige-laurel"]={262, 220, 0.623047, 0.878906, 0.*********, 0.21582, false, false},
		["honorsystem-talents-bg"]={635, 383, 0.*********, 0.621094, 0.*********, 0.375, false, false},
		["honorsystem-bar-frame-small"]={314, 43, 0.511719, 0.818359, 0.376953, 0.418945, false, false},
		["honorsystem-bar-rewardborder-circle"]={48, 48, 0.94043, 0.987305, 0.0546875, 0.101562, false, false},
		["honorsystem-icon-bonus"]={29, 29, 0.969727, 0.998047, 0.*********, 0.0292969, false, false},
		["honorsystem-bar-frame-exhaustiontick-highlight"]={17, 35, 0.976562, 0.993164, 0.115234, 0.149414, false, false},
		["honorsystem-bar-frame-exhaustiontick"]={17, 35, 0.958008, 0.974609, 0.115234, 0.149414, false, false},
		["honorsystem-prestige-smallwreath"]={89, 53, 0.880859, 0.967773, 0.*********, 0.0527344, false, false},
		["honorsystem-prestige-rewardline"]={276, 1, 0.588867, 0.858398, 0.422852, 0.423828, false, false},
		["honorsystem-bar-lock"]={36, 47, 0.880859, 0.916016, 0.115234, 0.161133, false, false},
		["honorsystem-icon-enlistmentbonus"]={29, 29, 0.880859, 0.90918, 0.163086, 0.191406, false, false},
	}, -- Interface/PVPFrame/PvPHonorSystem
	["Interface/PVPFrame/PvPHonorSystemHorizontal"]={
		["_honorsystem-bar-fill"]={256, 17, 0, 1, 0.015625, 0.28125, true, false},
		["_honorsystem-bar-fill-rested"]={256, 17, 0, 1, 0.3125, 0.578125, true, false},
	}, -- Interface/PVPFrame/PvPHonorSystemHorizontal
	["Interface/PVPFrame/PvPPrestigeIcons"]={
		["honorsystem-icon-prestige-1"]={128, 128, 0.*********, 0.125977, 0.********, 0.251953, false, false},
		["honorsystem-icon-prestige-10"]={128, 128, 0.*********, 0.125977, 0.255859, 0.505859, false, false},
		["honorsystem-icon-prestige-11"]={128, 128, 0.*********, 0.125977, 0.509766, 0.759766, false, false},
		["honorsystem-icon-prestige-2"]={128, 128, 0.12793, 0.25293, 0.********, 0.251953, false, false},
		["honorsystem-icon-prestige-3"]={128, 128, 0.12793, 0.25293, 0.255859, 0.505859, false, false},
		["honorsystem-icon-prestige-4"]={128, 128, 0.12793, 0.25293, 0.509766, 0.759766, false, false},
		["honorsystem-icon-prestige-5"]={128, 128, 0.254883, 0.379883, 0.********, 0.251953, false, false},
		["honorsystem-icon-prestige-6"]={128, 128, 0.254883, 0.379883, 0.255859, 0.505859, false, false},
		["honorsystem-icon-prestige-7"]={128, 128, 0.254883, 0.379883, 0.509766, 0.759766, false, false},
		["honorsystem-icon-prestige-8"]={128, 128, 0.381836, 0.506836, 0.********, 0.251953, false, false},
		["honorsystem-icon-prestige-9"]={128, 128, 0.381836, 0.506836, 0.255859, 0.505859, false, false},
		["honorsystem-portrait-alliance"]={50, 52, 0.*********, 0.0498047, 0.763672, 0.865234, false, false},
		["honorsystem-portrait-horde"]={50, 52, 0.*********, 0.0498047, 0.869141, 0.970703, false, false},
		["honorsystem-portrait-neutral"]={50, 52, 0.0517578, 0.100586, 0.763672, 0.865234, false, false},
	}, -- Interface/PVPFrame/PvPPrestigeIcons
	["Interface/PVPFrame/PvPQueueBackgroundAlliance"]={
		["pvpqueue-bg-alliance"]={322, 229, 0.********, 0.630859, 0.********, 0.898438, false, false},
	}, -- Interface/PVPFrame/PvPQueueBackgroundAlliance
	["Interface/PVPFrame/PvPQueueBackgroundHorde"]={
		["pvpqueue-bg-horde"]={322, 229, 0.********, 0.630859, 0.********, 0.898438, false, false},
	}, -- Interface/PVPFrame/PvPQueueBackgroundHorde
	["Interface/PVPFrame/TitlePrestige"]={
		["titleprestige-glowcover"]={216, 216, 0.349609, 0.560547, 0.322266, 0.533203, false, false},
		["titleprestige-prestigeicon"]={89, 91, 0.894531, 0.981445, 0.*********, 0.0898438, false, false},
		["titleprestige-starglow"]={355, 359, 0.*********, 0.347656, 0.*********, 0.351562, false, false},
		["titleprestige-title"]={141, 43, 0.5625, 0.700195, 0.473633, 0.515625, false, false},
		["titleprestige-title2"]={140, 24, 0.759766, 0.896484, 0.535156, 0.558594, false, false},
		["titleprestige-wreath"]={262, 153, 0.5625, 0.818359, 0.322266, 0.47168, false, false},
		["titleprestige-title-bg"]={418, 76, 0.349609, 0.757812, 0.535156, 0.609375, false, false},
		["titleprestige-ember"]={27, 31, 0.273438, 0.299805, 0.353516, 0.383789, false, false},
		["titleprestige-wings"]={277, 327, 0.*********, 0.271484, 0.353516, 0.672852, false, false},
		["titleprestige-wings2"]={277, 327, 0.349609, 0.620117, 0.*********, 0.320312, false, false},
		["titleprestige-wings-white"]={277, 327, 0.*********, 0.271484, 0.674805, 0.994141, false, false},
		["titleprestige-wings2-white"]={277, 327, 0.62207, 0.892578, 0.*********, 0.320312, false, false},
		["titleprestige-starcrown"]={122, 41, 0.820312, 0.939453, 0.491211, 0.53125, false, false},
		["titleprestige-prestigeiconplate-alliance"]={158, 171, 0.820312, 0.974609, 0.322266, 0.489258, false, false},
		["titleprestige-prestigeiconplate-horde"]={158, 171, 0.349609, 0.503906, 0.611328, 0.77832, false, false},
	}, -- Interface/PVPFrame/TitlePrestige
	["Interface/PVPFrame/TournamentOrganizer"]={
		["tournamentarena-flag-large-blue-flash"]={128, 64, 0.379883, 0.504883, 0.161133, 0.223633, false, false},
		["tournamentarena-flag-large-blue"]={128, 64, 0.25293, 0.37793, 0.161133, 0.223633, false, false},
		["tournamentarena-flag-large-red-flash"]={128, 64, 0.633789, 0.758789, 0.161133, 0.223633, false, false},
		["tournamentarena-flag-large-red"]={128, 64, 0.506836, 0.631836, 0.161133, 0.223633, false, false},
		["tournamentarena-frame-bg-side-blue"]={8, 32, 0.362305, 0.370117, 0.*********, 0.0322266, false, false},
		["tournamentarena-frame-bg-side-red"]={8, 32, 0.352539, 0.360352, 0.*********, 0.0322266, false, false},
		["tournamentarena-frame-bot"]={512, 64, 0.25293, 0.75293, 0.733398, 0.795898, false, false},
		["tournamentarena-frame-botleft"]={64, 64, 0.760742, 0.823242, 0.161133, 0.223633, false, false},
		["tournamentarena-frame-botright"]={64, 64, 0.889648, 0.952148, 0.161133, 0.223633, false, false},
		["tournamentarena-frame-left"]={64, 256, 0.*********, 0.0634766, 0.733398, 0.983398, false, false},
		["tournamentarena-frame-right"]={64, 256, 0.0654297, 0.12793, 0.733398, 0.983398, false, false},
		["tournamentarena-frame-top"]={512, 64, 0.25293, 0.75293, 0.924805, 0.987305, false, false},
		["tournamentarena-frame-topleft"]={64, 64, 0.825195, 0.887695, 0.161133, 0.223633, false, false},
		["tournamentarena-frame-topright"]={64, 64, 0.756836, 0.819336, 0.352539, 0.415039, false, false},
		["tournamentarena-titlebackplate"]={512, 128, 0.25293, 0.75293, 0.797852, 0.922852, false, false},
		["CastBar"]={256, 64, 0.*********, 0.250977, 0.161133, 0.223633, false, false},
		["CurrentPlayer-Glow"]={128, 128, 0.317383, 0.442383, 0.0341797, 0.15918, false, false},
		["DarkTutorialFrameBackground"]={32, 32, 0.25293, 0.28418, 0.*********, 0.0322266, false, false},
		["DeathIcon"]={64, 128, 0.760742, 0.823242, 0.0341797, 0.15918, false, false},
		["HealerBadge"]={64, 64, 0.444336, 0.506836, 0.0341797, 0.0966797, false, false},
		["HealingDampening"]={256, 128, 0.508789, 0.758789, 0.0341797, 0.15918, false, false},
		["InterruptOverlay"]={256, 128, 0.*********, 0.250977, 0.225586, 0.350586, false, false},
		["RedCCIconGlow"]={128, 128, 0.825195, 0.950195, 0.0341797, 0.15918, false, false},
		["ScoreDivider"]={64, 128, 0.25293, 0.31543, 0.225586, 0.350586, false, false},
		["ScoreTeam"]={512, 128, 0.317383, 0.817383, 0.225586, 0.350586, false, false},
		["TankBadge"]={64, 64, 0.25293, 0.31543, 0.0341797, 0.0966797, false, false},
		["TargetCrosshairs"]={128, 128, 0.819336, 0.944336, 0.225586, 0.350586, false, false},
		["tournamentarena-frame-bg-corner-blue"]={32, 32, 0.319336, 0.350586, 0.*********, 0.0322266, false, false},
		["tournamentarena-frame-bg-corner-red"]={32, 32, 0.286133, 0.317383, 0.*********, 0.0322266, false, false},
		["UI-Feedback-Border"]={256, 32, 0.*********, 0.250977, 0.*********, 0.0322266, false, false},
		["UnitFrame-NoMana"]={256, 128, 0.*********, 0.250977, 0.352539, 0.477539, false, false},
		["UnitFrame-NoTrinket"]={256, 128, 0.25293, 0.50293, 0.352539, 0.477539, false, false},
		["UnitFrame"]={256, 128, 0.*********, 0.250977, 0.479492, 0.604492, false, false},
		["UnitFrame_CCOverlay-NoMana"]={256, 128, 0.*********, 0.250977, 0.606445, 0.731445, false, false},
		["UnitFrame_CCOverlay"]={256, 128, 0.504883, 0.754883, 0.352539, 0.477539, false, false},
		["UnitFrame_DeathOverlay-NoMana"]={256, 128, 0.*********, 0.250977, 0.0341797, 0.15918, false, false},
		["UnitFrame_DeathOverlay"]={256, 128, 0.25293, 0.50293, 0.479492, 0.604492, false, false},
		["UnitFrame_FocusFireOverlay-NoMana"]={256, 128, 0.25293, 0.50293, 0.606445, 0.731445, false, false},
		["UnitFrame_FocusFireOverlay"]={256, 128, 0.504883, 0.754883, 0.606445, 0.731445, false, false},
		["UnitFrame_CurrentPlayerGlow-NoMana"]={336, 121, 0.504883, 0.833008, 0.479492, 0.597656, false, false},
		["tournamentarena-winnerline"]={418, 1, 0.37207, 0.780273, 0.*********, 0.********, false, false},
	}, -- Interface/PVPFrame/TournamentOrganizer
	["Interface/PVPFrame/TournamentOrganizer2"]={
		["UnitFrame_CurrentPlayer_CCOverlay"]={512, 256, 0.*********, 0.500977, 0.*********, 0.250977, false, false},
		["UnitFrame_CurrentPlayer_DeathOverlay"]={512, 256, 0.*********, 0.500977, 0.25293, 0.50293, false, false},
		["UnitFrame_CurrentPlayer-NoMana"]={345, 130, 0.331055, 0.667969, 0.654297, 0.78125, false, false},
		["UnitFrame_CurrentPlayer-NoTrinket"]={345, 151, 0.*********, 0.337891, 0.504883, 0.652344, false, false},
		["UnitFrame_CurrentPlayer"]={345, 151, 0.50293, 0.839844, 0.25293, 0.400391, false, false},
		["UnitFrame_CurrentPlayerGlow"]={336, 142, 0.*********, 0.329102, 0.654297, 0.792969, false, false},
		["UnitFrame_CurrentPlayer_FocusFireOverlay-NoMana"]={371, 130, 0.339844, 0.702148, 0.504883, 0.631836, false, false},
		["UnitFrame_CurrentPlayer_FocusFireOverlay"]={372, 151, 0.50293, 0.866211, 0.*********, 0.148438, false, false},
		["UnitFrame_CurrentPlayer_CCOverlay-NoMana"]={330, 115, 0.*********, 0.323242, 0.794922, 0.907227, false, false},
		["UnitFrame_CurrentPlayer_DeathOverlay-NoMana"]={330, 115, 0.669922, 0.992188, 0.654297, 0.766602, false, false},
	}, -- Interface/PVPFrame/TournamentOrganizer2
	["Interface/QuestFrame/AutoQuest"]={
		["AutoQuest-badgeborder"]={44, 45, 0.015625, 0.703125, 0.015625, 0.71875, false, false},
	}, -- Interface/QuestFrame/AutoQuest
	["Interface/QuestFrame/BonusObjectives"]={
		["bonusobjectives-bar-bg"]={193, 18, 0.40625, 0.783203, 0.615234, 0.650391, false, false},
		["bonusobjectives-bar-fill"]={1, 18, 0.505859, 0.507812, 0.298828, 0.333984, false, false},
		["bonusobjectives-bar-frame"]={240, 51, 0.********, 0.470703, 0.511719, 0.611328, false, false},
		["bonusobjectives-bar-glow-ring"]={240, 51, 0.474609, 0.943359, 0.511719, 0.611328, false, false},
		["bonusobjectives-bar-glow"]={205, 31, 0.********, 0.402344, 0.615234, 0.675781, false, false},
		["bonusobjectives-bar-ring"]={81, 51, 0.789062, 0.947266, 0.0917969, 0.191406, false, false},
		["bonusobjectives-bar-sheen"]={97, 44, 0.789062, 0.978516, 0.********, 0.0878906, false, false},
		["bonusobjectives-bar-spark"]={10, 28, 0.482422, 0.501953, 0.298828, 0.353516, false, false},
		["bonusobjectives-bar-starburst"]={36, 36, 0.789062, 0.859375, 0.195312, 0.265625, false, false},
		["bonusobjectives-title-bg"]={418, 76, 0.********, 0.818359, 0.359375, 0.507812, false, false},
		["bonusobjectives-title-icon"]={155, 150, 0.482422, 0.785156, 0.********, 0.294922, false, false},
		["bonusobjectives-title-icon-honor"]={244, 181, 0.********, 0.478516, 0.********, 0.355469, false, false},
	}, -- Interface/QuestFrame/BonusObjectives
	["Interface/QuestFrame/ObjectiveTracker"]={
		["Rewards-Shadow"]={154, 16, 0.********, 0.302734, 0.597656, 0.660156, false, false},
		["OBJFX_LineBurst"]={86, 20, 0.164062, 0.332031, 0.667969, 0.746094, false, false},
		["OBJBonusBar-Top"]={230, 23, 0.********, 0.451172, 0.5, 0.589844, false, false},
		["OBJFX_LineGlow"]={230, 22, 0.455078, 0.904297, 0.5, 0.585938, false, false},
		["Objective-Header"]={297, 86, 0.********, 0.582031, 0.********, 0.339844, false, false},
		["Rewards-Top"]={167, 22, 0.667969, 0.994141, 0.347656, 0.433594, false, false},
		["OBJFX_StarBurst"]={64, 64, 0.714844, 0.839844, 0.********, 0.253906, false, false},
		["Objective-Nub"]={11, 10, 0.65625, 0.677734, 0.261719, 0.300781, false, false},
		["OBJFX_Glow"]={64, 64, 0.585938, 0.710938, 0.********, 0.253906, false, false},
		["Tracker-Check"]={16, 16, 0.621094, 0.652344, 0.261719, 0.324219, false, false},
		["Objective-Fail"]={16, 16, 0.585938, 0.617188, 0.261719, 0.324219, false, false},
		["Objective-ItemBorder"]={81, 30, 0.********, 0.160156, 0.667969, 0.785156, false, false},
		["OBJFX-BarGlow"]={339, 37, 0.********, 0.664062, 0.347656, 0.492188, false, false},
	}, -- Interface/QuestFrame/ObjectiveTracker
	["Interface/QuestFrame/QuestBackgroundHordeAlliance"]={
		["Quest-Alliance-WaxSeal"]={97, 90, 0.*********, 0.0957031, 0.799805, 0.887695, false, false},
		["Quest-Horde-WaxSeal"]={97, 90, 0.*********, 0.0957031, 0.889648, 0.977539, false, false},
		["QuestBG-Alliance"]={299, 407, 0.*********, 0.292969, 0.*********, 0.398438, false, false},
		["QuestBG-Horde"]={299, 407, 0.*********, 0.292969, 0.400391, 0.797852, false, false},
		["QuestBG-Legionfall"]={299, 407, 0.294922, 0.586914, 0.*********, 0.398438, false, false},
		["Quest-Legionfall-WaxSeal"]={96, 90, 0.0976562, 0.191406, 0.799805, 0.887695, false, false},
		["QuestBG-TheHandofFate"]={299, 407, 0.588867, 0.880859, 0.*********, 0.398438, false, false},
	}, -- Interface/QuestFrame/QuestBackgroundHordeAlliance
	["Interface/QuestFrame/QuestMapLogAtlas"]={
		["BackArrow-Brown"]={9, 17, 0.474609, 0.483398, 0.856445, 0.873047, false, false},
		["MapCornerShadow-Left"]={182, 30, 0.283203, 0.460938, 0.825195, 0.854492, false, false},
		["MapCornerShadow-Right"]={46, 53, 0.283203, 0.328125, 0.87793, 0.929688, false, false},
		["NoQuestsBackground"]={287, 464, 0.*********, 0.28125, 0.*********, 0.454102, false, false},
		["OptionsIcon-Brown"]={16, 16, 0.457031, 0.472656, 0.856445, 0.87207, false, false},
		["QuestCollapse-Hide-Down"]={32, 32, 0.283203, 0.314453, 0.931641, 0.962891, false, false},
		["QuestCollapse-Hide-Up"]={32, 32, 0.380859, 0.412109, 0.87793, 0.90918, false, false},
		["QuestCollapse-Show-Down"]={32, 32, 0.330078, 0.361328, 0.955078, 0.986328, false, false},
		["QuestCollapse-Show-Up"]={32, 32, 0.330078, 0.361328, 0.921875, 0.953125, false, false},
		["QuestDetails-RewardsBottomOverlay"]={287, 46, 0.283203, 0.563477, 0.77832, 0.823242, false, false},
		["QuestDetails-RewardsOverlay"]={287, 275, 0.283203, 0.563477, 0.456055, 0.724609, false, false},
		["QuestDetails-TopOverlay"]={287, 51, 0.283203, 0.563477, 0.726562, 0.776367, false, false},
		["QuestDetailsBackgrounds"]={287, 464, 0.*********, 0.28125, 0.456055, 0.90918, false, false},
		["QuestionMarkContinent-Icon"]={20, 27, 0.258789, 0.27832, 0.939453, 0.96582, false, false},
		["QuestionMarkContinent-IconHighlight"]={20, 27, 0.258789, 0.27832, 0.911133, 0.9375, false, false},
		["QuestLogBackground"]={287, 464, 0.283203, 0.563477, 0.*********, 0.454102, false, false},
		["StoryHeader-BG"]={262, 77, 0.*********, 0.256836, 0.911133, 0.986328, false, false},
		["StoryHeader-CheevoIcon"]={50, 43, 0.330078, 0.378906, 0.87793, 0.919922, false, false},
		["StoryHeader-Shadow"]={252, 9, 0.*********, 0.24707, 0.988281, 0.99707, false, false},
		["TaskPOI-Icon"]={19, 19, 0.436523, 0.455078, 0.856445, 0.875, false, false},
		["TaskPOI-IconHighlight"]={19, 19, 0.416016, 0.43457, 0.856445, 0.875, false, false},
		["TaskPOI-IconSelect"]={19, 19, 0.258789, 0.277344, 0.967773, 0.986328, false, false},
		["UI-OuterBorderButtonPatch"]={18, 22, 0.380859, 0.398438, 0.944336, 0.96582, false, false},
		["UI-SquareButtonBrown-Down"]={32, 32, 0.380859, 0.412109, 0.911133, 0.942383, false, false},
		["UI-SquareButtonBrown-Up"]={32, 32, 0.283203, 0.314453, 0.964844, 0.996094, false, false},
		["QuestItemBorder-Small"]={134, 20, 0.283203, 0.414062, 0.856445, 0.875977, false, false},
		["QuestItemBorder"]={102, 30, 0.462891, 0.5625, 0.825195, 0.854492, false, false},
	}, -- Interface/QuestFrame/QuestMapLogAtlas
	["Interface/QuestFrame/TalkingHeads"]={
		["TalkingHeads-Glow-TopSpike"]={46, 30, 0.505859, 0.550781, 0.617188, 0.734375, false, false},
		["TalkingHeads-PortraitBg"]={115, 115, 0.701172, 0.813477, 0.********, 0.453125, false, false},
		["TalkingHeads-TextBackground"]={570, 155, 0.*********, 0.557617, 0.********, 0.609375, false, false},
		["TalkingHeads-PortraitFrame"]={143, 143, 0.55957, 0.699219, 0.********, 0.5625, false, false},
		["TalkingHeads-Glow-TopBarGlow"]={81, 23, 0.*********, 0.0800781, 0.902344, 0.992188, false, false},
		["TalkingHeads-Glow-SideBarGlow"]={23, 86, 0.55957, 0.582031, 0.570312, 0.90625, false, false},
		["TalkingHeads-Glow-Sheen"]={262, 34, 0.248047, 0.503906, 0.617188, 0.75, false, false},
		["TalkingHeads-Glow-TextSheen"]={251, 71, 0.*********, 0.246094, 0.617188, 0.894531, false, false},
	}, -- Interface/QuestFrame/TalkingHeads
	["Interface/QuestFrame/WorldQuest"]={
		["worldquest-icon-clock"]={22, 22, 0.75, 0.792969, 0.71875, 0.890625, false, false},
		["worldquest-questmarker-dragon-glow"]={49, 48, 0.********, 0.0976562, 0.578125, 0.953125, false, false},
		["worldquest-questmarker-dragon"]={34, 34, 0.623047, 0.689453, 0.695312, 0.960938, false, false},
		["worldquest-questmarker-epic-down"]={20, 20, 0.855469, 0.894531, 0.0078125, 0.164062, false, false},
		["worldquest-questmarker-epic"]={20, 20, 0.8125, 0.851562, 0.0078125, 0.164062, false, false},
		["worldquest-questmarker-glow"]={36, 36, 0.548828, 0.619141, 0.695312, 0.976562, false, false},
		["worldquest-questmarker-questbang"]={8, 17, 0.84375, 0.859375, 0.671875, 0.804688, false, false},
		["worldquest-tracker-checkmark"]={40, 35, 0.441406, 0.519531, 0.578125, 0.851562, false, false},
		["worldquest-tracker-questmarker-gray"]={25, 25, 0.75, 0.798828, 0.507812, 0.703125, false, false},
		["worldquest-tracker-questmarker"]={25, 25, 0.693359, 0.742188, 0.695312, 0.890625, false, false},
		["worldquest-tracker"]={278, 71, 0.********, 0.544922, 0.0078125, 0.5625, false, false},
		["worldquest-questmarker-questionmark"]={13, 17, 0.882812, 0.908203, 0.179688, 0.3125, false, false},
		["worldquest-icon-pvp-ffa"]={14, 14, 0.8125, 0.839844, 0.328125, 0.4375, false, false},
		["worldquest-tracker-ring-selected"]={44, 44, 0.191406, 0.277344, 0.578125, 0.921875, false, false},
		["worldquest-tracker-ring"]={44, 44, 0.101562, 0.1875, 0.578125, 0.921875, false, false},
		["worldquest-icon-alchemy"]={12, 14, 0.974609, 0.998047, 0.179688, 0.289062, false, false},
		["worldquest-icon-archaeology"]={14, 14, 0.943359, 0.970703, 0.179688, 0.289062, false, false},
		["worldquest-icon-blacksmithing"]={14, 15, 0.515625, 0.542969, 0.867188, 0.984375, false, false},
		["worldquest-icon-cooking"]={11, 15, 0.523438, 0.544922, 0.578125, 0.695312, false, false},
		["worldquest-icon-enchanting"]={12, 13, 0.8125, 0.835938, 0.804688, 0.90625, false, false},
		["worldquest-icon-engineering"]={14, 15, 0.912109, 0.939453, 0.179688, 0.296875, false, false},
		["worldquest-icon-firstaid"]={10, 10, 0.724609, 0.744141, 0.90625, 0.984375, false, false},
		["worldquest-icon-fishing"]={13, 13, 0.8125, 0.837891, 0.570312, 0.671875, false, false},
		["worldquest-icon-herbalism"]={13, 13, 0.8125, 0.837891, 0.6875, 0.789062, false, false},
		["worldquest-icon-inscription"]={11, 14, 0.523438, 0.544922, 0.710938, 0.820312, false, false},
		["worldquest-icon-jewelcrafting"]={14, 11, 0.693359, 0.720703, 0.90625, 0.992188, false, false},
		["worldquest-icon-leatherworking"]={13, 12, 0.84375, 0.869141, 0.328125, 0.421875, false, false},
		["worldquest-icon-mining"]={12, 13, 0.84375, 0.867188, 0.4375, 0.539062, false, false},
		["worldquest-icon-skinning"]={14, 13, 0.8125, 0.839844, 0.453125, 0.554688, false, false},
		["worldquest-icon-tailoring"]={12, 13, 0.84375, 0.867188, 0.554688, 0.65625, false, false},
		["worldquest-icon-petbattle"]={13, 11, 0.75, 0.775391, 0.90625, 0.992188, false, false},
		["worldquest-icon-boss"]={11, 12, 0.873047, 0.894531, 0.328125, 0.421875, false, false},
		["worldquest-followerabilityframe"]={101, 86, 0.548828, 0.746094, 0.0078125, 0.679688, false, false},
		["worldquest-questmarker-abilityhighlight"]={42, 42, 0.28125, 0.363281, 0.578125, 0.90625, false, false},
		["worldquest-tracker-lock"]={36, 47, 0.367188, 0.4375, 0.578125, 0.945312, false, false},
		["worldquest-questmarker-rare-down"]={20, 20, 0.941406, 0.980469, 0.0078125, 0.164062, false, false},
		["worldquest-questmarker-rare"]={20, 20, 0.898438, 0.9375, 0.0078125, 0.164062, false, false},
		["worldquest-tracker-bg-noemissary"]={30, 30, 0.75, 0.808594, 0.0078125, 0.242188, false, false},
		["worldquest-icon-dungeon"]={16, 17, 0.8125, 0.84375, 0.179688, 0.3125, false, false},
		["worldquest-icon-burninglegion"]={16, 15, 0.480469, 0.511719, 0.867188, 0.984375, false, false},
		["worldquest-icon-raid"]={16, 17, 0.847656, 0.878906, 0.179688, 0.3125, false, false},
		["worldquest-emissary-ring"]={30, 30, 0.75, 0.808594, 0.257812, 0.492188, false, false},
		["worldquest-emissary-tracker-checkmark"]={18, 16, 0.441406, 0.476562, 0.867188, 0.992188, false, false},
	}, -- Interface/QuestFrame/WorldQuest
	["Interface/QuestionFrame/Warboard"]={
		["warboard-header-alliance"]={708, 210, 0.*********, 0.692383, 0.255859, 0.460938, false, false},
		["warboard-header-horde"]={638, 240, 0.344727, 0.967773, 0.462891, 0.697266, false, false},
		["warboard-parchment-answerborder"]={190, 93, 0.694336, 0.879883, 0.34082, 0.431641, false, false},
		["warboard-parchment"]={350, 473, 0.*********, 0.342773, 0.462891, 0.924805, false, false},
		["warboard-title-alliance-left"]={202, 85, 0.543945, 0.741211, 0.699219, 0.782227, false, false},
		["warboard-title-alliance-right"]={202, 85, 0.344727, 0.541992, 0.699219, 0.782227, false, false},
		["warboard-title-horde-left"]={202, 85, 0.694336, 0.891602, 0.255859, 0.338867, false, false},
		["warboard-title-horde-right"]={202, 85, 0.743164, 0.94043, 0.699219, 0.782227, false, false},
		["warboard-title-neutral-left"]={202, 85, 0.344727, 0.541992, 0.78418, 0.867188, false, false},
		["warboard-title-neutral-right"]={202, 85, 0.344727, 0.541992, 0.869141, 0.952148, false, false},
		["_warboard-title-alliance-middle"]={128, 85, 0, 0.125, 0.*********, 0.0839844, true, false},
		["_warboard-title-horde-middle"]={128, 85, 0, 0.125, 0.170898, 0.253906, true, false},
		["_warboard-title-neutral-middle"]={128, 85, 0, 0.125, 0.0859375, 0.168945, true, false},
		["warboard-parchment-nail"]={46, 45, 0.893555, 0.938477, 0.255859, 0.299805, false, false},
	}, -- Interface/QuestionFrame/Warboard
	["Interface/QuestionFrame/WarboardBackground"]={
		["warboard-background"]={597, 502, 0.*********, 0.583984, 0.********, 0.982422, false, false},
	}, -- Interface/QuestionFrame/WarboardBackground
	["Interface/Scenarios/LegionInvasion"]={
		["legioninvasion-map-icon-portal-large"]={59, 62, 0.613281, 0.728516, 0.28125, 0.402344, false, false},
		["legioninvasion-map-icon-portal"]={42, 44, 0.830078, 0.912109, 0.28125, 0.367188, false, false},
		["legioninvasion-scenario-rewardring"]={48, 48, 0.732422, 0.826172, 0.28125, 0.375, false, false},
		["legioninvasion-ScenarioTrackerToast"]={243, 77, 0.********, 0.476562, 0.4375, 0.587891, false, false},
		["legioninvasion-Toast-Frame"]={311, 78, 0.********, 0.609375, 0.28125, 0.433594, false, false},
		["legioninvasion-title-bg"]={467, 141, 0.********, 0.914062, 0.********, 0.277344, false, false},
	}, -- Interface/Scenarios/LegionInvasion
	["Interface/Scenarios/LegionInvasionMap"]={
		["legioninvasion-map-cover"]={1004, 672, 0.*********, 0.981445, 0.*********, 0.657227, false, false},
	}, -- Interface/Scenarios/LegionInvasionMap
	["Interface/Scenarios/ScenarioParts"]={
		["Banner-BgFiligree"]={249, 31, 0.********, 0.488281, 0.572266, 0.632812, false, false},
		["Banner-FiligreeShadow"]={382, 35, 0.********, 0.748047, 0.5, 0.568359, false, false},
		["Banner-SmallFiligree"]={61, 19, 0.837891, 0.957031, 0.********, 0.0390625, false, false},
		["ScenariosIcon"]={48, 47, 0.740234, 0.833984, 0.********, 0.09375, false, false},
		["ScenarioTrackerToast-FinalFiligree"]={235, 69, 0.480469, 0.939453, 0.345703, 0.480469, false, false},
		["ScenarioTrackerToast"]={243, 77, 0.********, 0.476562, 0.345703, 0.496094, false, false},
		["Toast-Flash"]={327, 94, 0.********, 0.640625, 0.********, 0.185547, false, false},
		["Toast-Frame"]={311, 78, 0.********, 0.609375, 0.189453, 0.341797, false, false},
		["Toast-IconBG"]={47, 52, 0.644531, 0.736328, 0.********, 0.103516, false, false},
		["Bonus-ToastBanner"]={36, 44, 0.740234, 0.810547, 0.0976562, 0.183594, false, false},
	}, -- Interface/Scenarios/ScenarioParts
	["Interface/SpellBook/GlyphIconSpellbook"]={
		["GlyphIcon-Spellbook"]={21, 22, 0.03125, 0.6875, 0.03125, 0.71875, false, false},
	}, -- Interface/SpellBook/GlyphIconSpellbook
	["Interface/Splash/Splash"]={
		["splash-bigbutton"]={356, 99, 0.********, 0.697266, 0.140625, 0.914062, false, false},
		["splash-botleft"]={368, 15, 0.********, 0.720703, 0.0078125, 0.125, false, false},
	}, -- Interface/Splash/Splash
	["Interface/Splash/Splash600"]={
		["splash-600-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-600-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-600-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash600
	["Interface/Splash/Splash601"]={
		["splash-601-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-601-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-601-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash601
	["Interface/Splash/Splash610"]={
		["splash-610-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-610-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-610-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash610
	["Interface/Splash/Splash620"]={
		["splash-620-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-620-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-620-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash620
	["Interface/Splash/Splash703"]={
		["splash-703-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-703-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-703-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash703
	["Interface/Splash/Splash704"]={
		["splash-704-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-704-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-704-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash704
	["Interface/Splash/Splash705"]={
		["splash-705-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-705-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-705-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash705
	["Interface/Splash/Splash710"]={
		["splash-710-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-710-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-710-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash710
	["Interface/Splash/Splash720"]={
		["splash-720-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-720-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-720-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash720
	["Interface/Splash/Splash725"]={
		["splash-725-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-725-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-725-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash725
	["Interface/Splash/Splash730"]={
		["splash-730-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-730-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-730-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash730
	["Interface/Splash/Splash735"]={
		["splash-735-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-735-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-735-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/Splash735
	["Interface/Splash/SplashBoost"]={
		["splash-boost-botleft"]={137, 371, 0.730469, 0.998047, 0.573242, 0.935547, false, false},
		["splash-boost-right"]={510, 584, 0.********, 0.998047, 0.*********, 0.571289, false, false},
		["splash-boost-topleft"]={371, 434, 0.********, 0.726562, 0.573242, 0.99707, false, false},
	}, -- Interface/Splash/SplashBoost
	["Interface/Store/BoostPopup"]={
		["services-popup-bot"]={198, 44, 0.392578, 0.779297, 0.419922, 0.505859, false, false},
		["services-popup-botleft"]={104, 44, 0.775391, 0.978516, 0.244141, 0.330078, false, false},
		["services-popup-botright"]={104, 44, 0.783203, 0.986328, 0.419922, 0.505859, false, false},
		["services-popup-top"]={198, 60, 0.********, 0.388672, 0.419922, 0.537109, false, false},
		["services-popup-topleft"]={106, 60, 0.775391, 0.982422, 0.********, 0.119141, false, false},
		["services-popup-topright"]={106, 60, 0.775391, 0.982422, 0.123047, 0.240234, false, false},
		["services-popup-bg"]={394, 212, 0.********, 0.771484, 0.********, 0.416016, false, false},
		["services-popup-left"]={64, 32, 0.775391, 0.900391, 0.333984, 0.396484, false, false},
		["services-popup-right"]={64, 32, 0.********, 0.126953, 0.541016, 0.603516, false, false},
	}, -- Interface/Store/BoostPopup
	["Interface/Store/BoostPopupLegion"]={
		["Legion-boostpopup-bottom"]={430, 70, 0.********, 0.841797, 0.722656, 0.996094, false, false},
		["Legion-boostpopup-middle"]={430, 108, 0.********, 0.841797, 0.********, 0.425781, false, false},
		["Legion-boostpopup-top"]={430, 72, 0.********, 0.841797, 0.433594, 0.714844, false, false},
		["Legion-boostpopup-exit-frame"]={25, 24, 0.845703, 0.894531, 0.********, 0.0976562, false, false},
	}, -- Interface/Store/BoostPopupLegion
	["Interface/Store/BoostPopupWoD"]={
		["boostpopup-wod-bottom"]={430, 65, 0.********, 0.841797, 0.472656, 0.726562, false, false},
		["boostpopup-wod-middle"]={430, 118, 0.********, 0.841797, 0.********, 0.464844, false, false},
		["boostpopup-wod-top"]={430, 65, 0.********, 0.841797, 0.734375, 0.988281, false, false},
	}, -- Interface/Store/BoostPopupWoD
	["Interface/Store/ServicesAtlas"]={
		["services-icon-warning"]={42, 37, 0.*********, 0.0419922, 0.961914, 0.998047, false, false},
		["services-icon-goldborder"]={62, 62, 0.805664, 0.866211, 0.0800781, 0.140625, false, false},
		["services-checkmark"]={41, 44, 0.948242, 0.988281, 0.*********, 0.0439453, false, false},
		["services-cover-ring"]={80, 82, 0.25, 0.328125, 0.84668, 0.926758, false, false},
		["services-cover"]={421, 724, 0.*********, 0.412109, 0.*********, 0.708008, false, false},
		["services-icon-bonus-large"]={34, 34, 0.0869141, 0.120117, 0.961914, 0.995117, false, false},
		["services-icon-bonus-red"]={19, 19, 0.174805, 0.193359, 0.961914, 0.980469, false, false},
		["services-icon-bonus"]={19, 19, 0.195312, 0.213867, 0.961914, 0.980469, false, false},
		["services-icon-processing"]={42, 37, 0.0439453, 0.0849609, 0.961914, 0.998047, false, false},
		["services-number-1"]={71, 79, 0.330078, 0.399414, 0.84668, 0.923828, false, false},
		["services-number-2"]={71, 79, 0.663086, 0.732422, 0.*********, 0.078125, false, false},
		["services-number-3"]={71, 79, 0.663086, 0.732422, 0.0800781, 0.157227, false, false},
		["services-number-4"]={71, 79, 0.663086, 0.732422, 0.15918, 0.236328, false, false},
		["services-number-5"]={71, 79, 0.734375, 0.803711, 0.*********, 0.078125, false, false},
		["services-number-6"]={71, 79, 0.734375, 0.803711, 0.0800781, 0.157227, false, false},
		["services-number-7"]={71, 79, 0.734375, 0.803711, 0.15918, 0.236328, false, false},
		["services-number-8"]={71, 79, 0.805664, 0.875, 0.*********, 0.078125, false, false},
		["services-number-9"]={71, 79, 0.876953, 0.946289, 0.*********, 0.078125, false, false},
		["services-ring-countcircle"]={32, 32, 0.12207, 0.15332, 0.961914, 0.993164, false, false},
		["services-ring-large-glow"]={253, 256, 0.*********, 0.248047, 0.709961, 0.959961, false, false},
		["services-ring-large-glowpulse"]={145, 138, 0.25, 0.391602, 0.709961, 0.844727, false, false},
		["services-ring-large-glowspin"]={253, 256, 0.414062, 0.661133, 0.*********, 0.250977, false, false},
		["services-ring-large"]={72, 73, 0.330078, 0.400391, 0.925781, 0.99707, false, false},
		["services-ring-wod"]={72, 68, 0.25, 0.320312, 0.928711, 0.995117, false, false},
		["services-ring"]={59, 60, 0.805664, 0.863281, 0.142578, 0.201172, false, false},
		["services-yellowarrow"]={18, 32, 0.155273, 0.172852, 0.961914, 0.993164, false, false},
	}, -- Interface/Store/ServicesAtlas
	["Interface/Store/Shop"]={
		["vas-receipt-selectedcharbg"]={302, 72, 0.291016, 0.880859, 0.********, 0.285156, false, false},
		["shop-card-darkcover"]={146, 209, 0.********, 0.287109, 0.********, 0.820312, false, false},
		["vas-receipt-brownarrow"]={30, 15, 0.0722656, 0.130859, 0.828125, 0.886719, false, false},
		["vas-receipt-greenbg"]={297, 46, 0.291016, 0.871094, 0.292969, 0.472656, false, false},
		["category-icon-ring"]={74, 74, 0.291016, 0.435547, 0.480469, 0.769531, false, false},
		["vas-receipt-icon-characterborder"]={34, 34, 0.********, 0.0683594, 0.828125, 0.960938, false, false},
	}, -- Interface/Store/Shop
	["Interface/Store/ShopBundles"]={
		["shop-card-bundle-hover"]={284, 201, 0.288086, 0.56543, 0.********, 0.394531, false, false},
		["shop-card-bundle-legion"]={292, 158, 0.*********, 0.286133, 0.414062, 0.722656, false, false},
		["shop-card-bundle-reaver-argi"]={292, 145, 0.567383, 0.852539, 0.********, 0.285156, false, false},
		["shop-card-bundle"]={292, 209, 0.*********, 0.286133, 0.********, 0.410156, false, false},
		["shop-card-bundle-selected"]={284, 201, 0.288086, 0.56543, 0.398438, 0.791016, false, false},
		["shop-card-bundle-alteracpup-feydragon"]={292, 153, 0.567383, 0.852539, 0.572266, 0.871094, false, false},
		["shop-card-bundle-starseekers"]={292, 143, 0.567383, 0.852539, 0.289062, 0.568359, false, false},
	}, -- Interface/Store/ShopBundles
	["Interface/Store/ShopGames"]={
		["shop-games-legion"]={138, 86, 0.291016, 0.560547, 0.5, 0.835938, false, false},
		["shop-games-legiondeluxe-card"]={146, 209, 0.********, 0.287109, 0.********, 0.820312, false, false},
		["shop-games-magnifyingglass"]={46, 47, 0.564453, 0.654297, 0.********, 0.1875, false, false},
		["shop-games-legiondeluxe"]={138, 125, 0.291016, 0.560547, 0.********, 0.492188, false, false},
	}, -- Interface/Store/ShopGames
	["Interface/Store/ShopGameUpgrades"]={
		["shop-card-half-hover"]={278, 463, 0.********, 0.544922, 0.********, 0.90625, false, false},
	}, -- Interface/Store/ShopGameUpgrades
	["Interface/Store/ShopGameUpgradesBrownBox"]={
		["shop-card-half-brownbox"]={286, 471, 0.*********, 0.280273, 0.********, 0.921875, false, false},
		["shop-card-half-brownboxlegionbundle"]={286, 471, 0.282227, 0.561523, 0.********, 0.921875, false, false},
	}, -- Interface/Store/ShopGameUpgradesBrownBox
	["Interface/Store/ShopGameUpgradesLegion"]={
		["shop-card-half-legion"]={286, 471, 0.*********, 0.280273, 0.********, 0.921875, false, false},
		["shop-card-half-legiondeluxe"]={286, 471, 0.282227, 0.561523, 0.********, 0.921875, false, false},
	}, -- Interface/Store/ShopGameUpgradesLegion
	["Interface/Store/ShopGameUpgradesLegionDeluxe"]={
		["shop-card-full-legiondeluxe"]={568, 463, 0.*********, 0.555664, 0.********, 0.90625, false, false},
	}, -- Interface/Store/ShopGameUpgradesLegionDeluxe
	["Interface/Store/SimpleCheckout"]={
		["simplecheckout-close-hover-1x"]={20, 20, 0.0078125, 0.164062, 0.671875, 0.984375, false, false},
		["simplecheckout-close-hover-2x"]={40, 40, 0.0078125, 0.320312, 0.015625, 0.640625, false, false},
		["simplecheckout-close-normal-1x"]={20, 20, 0.335938, 0.492188, 0.671875, 0.984375, false, false},
		["simplecheckout-close-normal-2x"]={40, 40, 0.335938, 0.648438, 0.015625, 0.640625, false, false},
		["simplecheckout-close-pressed-1x"]={20, 20, 0.664062, 0.820312, 0.671875, 0.984375, false, false},
		["simplecheckout-close-pressed-2x"]={40, 40, 0.664062, 0.976562, 0.015625, 0.640625, false, false},
	}, -- Interface/Store/SimpleCheckout
	["Interface/TalentFrame/TalentFrameAtlas"]={
		["Talent-Separator"]={68, 50, 0.********, 0.269531, 0.732422, 0.830078, false, false},
		["Talent-BottomLeftCurlies"]={65, 55, 0.484375, 0.738281, 0.203125, 0.310547, false, false},
		["Talent-Highlight"]={200, 53, 0.********, 0.785156, 0.441406, 0.544922, false, false},
		["Talent-TopRightCurlies"]={64, 55, 0.746094, 0.996094, 0.203125, 0.310547, false, false},
		["Talent-TopLeftCurlies"]={63, 55, 0.746094, 0.992188, 0.314453, 0.421875, false, false},
		["Talent-Background"]={32, 43, 0, 0.125, 0.********, 0.0859375, true, false},
		["Talent-Selection"]={190, 45, 0.********, 0.746094, 0.548828, 0.636719, false, false},
		["Talent-RingWithDot"]={121, 120, 0.********, 0.476562, 0.203125, 0.4375, false, false},
		["Talent-BottomRightCurlies"]={65, 55, 0.484375, 0.738281, 0.314453, 0.421875, false, false},
		["_Talent-blue-glow"]={16, 16, 0, 0.0625, 0.0898438, 0.121094, true, false},
		["_Talent-Bottom-Tile"]={64, 5, 0, 0.25, 0.189453, 0.199219, true, false},
		["_Talent-Top-Tile"]={64, 13, 0, 0.25, 0.160156, 0.185547, true, false},
		["_Talent-green-glow"]={16, 16, 0, 0.0625, 0.125, 0.15625, true, false},
		["Talent-Selection-Legendary"]={190, 45, 0.********, 0.746094, 0.640625, 0.728516, false, false},
	}, -- Interface/TalentFrame/TalentFrameAtlas
	["Interface/TargetingFrame/Nameplates"]={
		["nameplates-bar-background"]={254, 13, 0.********, 0.996094, 0.0078125, 0.109375, false, false},
		["nameplates-holypower1-off"]={25, 19, 0.15625, 0.253906, 0.351562, 0.5, false, false},
		["nameplates-holypower1-on"]={25, 19, 0.15625, 0.253906, 0.515625, 0.664062, false, false},
		["nameplates-holypower2-off"]={26, 17, 0.261719, 0.363281, 0.351562, 0.484375, false, false},
		["nameplates-holypower2-on"]={26, 17, 0.261719, 0.363281, 0.5, 0.632812, false, false},
		["nameplates-holypower3-off"]={24, 17, 0.15625, 0.25, 0.679688, 0.8125, false, false},
		["nameplates-holypower3-on"]={24, 17, 0.15625, 0.25, 0.828125, 0.960938, false, false},
		["nameplates-holypower4-off"]={25, 14, 0.261719, 0.359375, 0.648438, 0.757812, false, false},
		["nameplates-holypower4-on"]={25, 14, 0.261719, 0.359375, 0.773438, 0.882812, false, false},
		["nameplates-playerhealth-background"]={126, 12, 0.********, 0.496094, 0.242188, 0.335938, false, false},
		["nameplates-InterruptShield"]={14, 16, 0.371094, 0.425781, 0.351562, 0.476562, false, false},
		["nameplates-bar-background-white"]={254, 13, 0.********, 0.996094, 0.125, 0.226562, false, false},
		["nameplates-icon-elite-gold"]={37, 35, 0.********, 0.148438, 0.351562, 0.625, false, false},
		["nameplates-icon-elite-silver"]={37, 35, 0.********, 0.148438, 0.640625, 0.914062, false, false},
	}, -- Interface/TargetingFrame/Nameplates
	["Interface/TaxiFrame/ArgusTaxi"]={
		["FlightMaster_Argus-Taxi_Frame_Gray"]={64, 54, 0.447266, 0.572266, 0.********, 0.214844, false, false},
		["FlightMaster_Argus-Taxi_Frame_Yellow"]={64, 54, 0.576172, 0.701172, 0.********, 0.214844, false, false},
		["FlightMaster_VindicaarArgus-Taxi_Frame_Gray"]={74, 79, 0.********, 0.146484, 0.********, 0.3125, false, false},
		["FlightMaster_VindicaarArgus-Taxi_Frame_Green"]={74, 79, 0.********, 0.146484, 0.320312, 0.628906, false, false},
		["FlightMaster_VindicaarArgus-Taxi_Frame_Yellow"]={74, 79, 0.********, 0.146484, 0.636719, 0.945312, false, false},
		["FlightMaster_VindicaarMacAree-Taxi_Frame_Gray"]={74, 79, 0.150391, 0.294922, 0.********, 0.3125, false, false},
		["FlightMaster_VindicaarMacAree-Taxi_Frame_Green"]={74, 79, 0.150391, 0.294922, 0.320312, 0.628906, false, false},
		["FlightMaster_VindicaarMacAree-Taxi_Frame_Yellow"]={74, 79, 0.150391, 0.294922, 0.636719, 0.945312, false, false},
		["FlightMaster_VindicaarStygianWake-Taxi_Frame_Gray"]={74, 79, 0.298828, 0.443359, 0.********, 0.3125, false, false},
		["FlightMaster_VindicaarStygianWake-Taxi_Frame_Green"]={74, 79, 0.298828, 0.443359, 0.320312, 0.628906, false, false},
		["FlightMaster_VindicaarStygianWake-Taxi_Frame_Yellow"]={74, 79, 0.298828, 0.443359, 0.636719, 0.945312, false, false},
		["FlightMaster_Argus-Taxi_Frame_Green"]={64, 54, 0.705078, 0.830078, 0.********, 0.214844, false, false},
	}, -- Interface/TaxiFrame/ArgusTaxi
	["Interface/TaxiFrame/taxiassets"]={
		["Taxi_Frame_Gray"]={74, 74, 0.0078125, 0.585938, 0.0683594, 0.212891, false, false},
		["Taxi_Frame_Green"]={74, 74, 0.0078125, 0.585938, 0.216797, 0.361328, false, false},
		["UI-Taxi-Icon-Nub"]={16, 16, 0.601562, 0.726562, 0.0683594, 0.0996094, false, false},
		["_UI-Taxi-Line-horizontal"]={32, 32, 0, 0.25, 0.********, 0.0644531, true, false},
		["Taxi_Frame_Yellow"]={74, 74, 0.0078125, 0.585938, 0.365234, 0.509766, false, false},
	}, -- Interface/TaxiFrame/taxiassets
	["Interface/TaxiFrame/taxiassets2"]={
		["!UI-Taxi-Line"]={32, 32, 0.015625, 0.515625, 0, 1, false, true},
	}, -- Interface/TaxiFrame/taxiassets2
	["Interface/Timer/Countdown"]={
		["countdown-swords"]={256, 256, 0.*********, 0.250977, 0.********, 0.501953, false, false},
		["countdown-swords-glow"]={256, 256, 0.25293, 0.50293, 0.********, 0.501953, false, false},
	}, -- Interface/Timer/Countdown
	["Interface/Tooltips/AchievementCompare"]={
		["achievementcompare-GreenCheckmark"]={9, 11, 0.03125, 0.3125, 0.0625, 0.75, false, false},
		["achievementcompare-YellowCheckmark"]={9, 11, 0.375, 0.65625, 0.0625, 0.75, false, false},
	}, -- Interface/Tooltips/AchievementCompare
	["Interface/TradeSkillFrame/CapacitanceUIBlacksmithing"]={
		["Capacitance-Blacksmithing-BG"]={321, 370, 0.********, 0.628906, 0.********, 0.724609, false, false},
		["Capacitance-Blacksmithing-IconBG"]={291, 58, 0.********, 0.570312, 0.728516, 0.841797, false, false},
		["Capacitance-Blacksmithing-IconBorder"]={59, 58, 0.632812, 0.748047, 0.********, 0.115234, false, false},
		["Capacitance-Blacksmithing-TimerBG"]={275, 15, 0.********, 0.539062, 0.912109, 0.941406, false, false},
		["Capacitance-Blacksmithing-TimerFill"]={275, 15, 0.********, 0.539062, 0.945312, 0.974609, false, false},
		["Capacitance-Blacksmithing-TimerFrame"]={292, 32, 0.********, 0.572266, 0.845703, 0.908203, false, false},
	}, -- Interface/TradeSkillFrame/CapacitanceUIBlacksmithing
	["Interface/TradeSkillFrame/CapacitanceUIGeneral"]={
		["Capacitance-General-EmptyFollower"]={59, 62, 0.********, 0.234375, 0.34375, 0.828125, false, false},
		["Capacitance-General-ItemBorder"]={133, 41, 0.********, 0.523438, 0.0078125, 0.328125, false, false},
		["Capacitance-General-LockIcon"]={22, 29, 0.910156, 0.996094, 0.0078125, 0.234375, false, false},
		["Capacitance-General-WorkOrderActive"]={48, 48, 0.480469, 0.667969, 0.34375, 0.71875, false, false},
		["Capacitance-General-WorkOrderArrow"]={16, 10, 0.53125, 0.59375, 0.210938, 0.289062, false, false},
		["Capacitance-General-WorkOrderBorder"]={34, 34, 0.769531, 0.902344, 0.0078125, 0.273438, false, false},
		["Capacitance-General-WorkOrderCheckmark"]={36, 32, 0.480469, 0.621094, 0.734375, 0.984375, false, false},
		["Capacitance-General-WorkOrderEmpty"]={34, 34, 0.675781, 0.808594, 0.34375, 0.609375, false, false},
		["Capacitance-General-LevelBorder"]={59, 24, 0.53125, 0.761719, 0.0078125, 0.195312, false, false},
		["Capacitance-General-PortraitRing"]={59, 62, 0.242188, 0.472656, 0.34375, 0.828125, false, false},
	}, -- Interface/TradeSkillFrame/CapacitanceUIGeneral
	["Interface/TradeSkillFrame/ObliterumForge"]={
		["obliterumforge-background"]={322, 160, 0.********, 0.630859, 0.********, 0.628906, false, false},
		["obliterumforge-slotted-corners-glow"]={132, 60, 0.634766, 0.892578, 0.********, 0.238281, false, false},
		["obliterumforge-slotted-corners"]={132, 60, 0.634766, 0.892578, 0.246094, 0.480469, false, false},
	}, -- Interface/TradeSkillFrame/ObliterumForge
	["Interface/TradeSkillFrame/Tradeskills"]={
		["tradeskills-star-off"]={20, 19, 0.0224609, 0.0419922, 0.847656, 0.884766, false, false},
		["tradeskills-star"]={20, 19, 0.*********, 0.0205078, 0.847656, 0.884766, false, false},
		["tradeskill-background-recipe"]={300, 383, 0.*********, 0.293945, 0.********, 0.75, false, false},
		["tradeskill-background-recipe-unlearned"]={300, 383, 0.295898, 0.588867, 0.********, 0.75, false, false},
		["tradeskills-iconborder"]={46, 46, 0.*********, 0.0458984, 0.753906, 0.84375, false, false},
	}, -- Interface/TradeSkillFrame/Tradeskills
	["Interface/Transmogrify/TransmogBackgroundBloodElf"]={
		["transmog-background-race-bloodelf"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundBloodElf
	["Interface/Transmogrify/TransmogBackgroundDraenei"]={
		["transmog-background-race-draenei"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundDraenei
	["Interface/Transmogrify/TransmogBackgroundDwarf"]={
		["transmog-background-race-dwarf"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundDwarf
	["Interface/Transmogrify/TransmogBackgroundGnome"]={
		["transmog-background-race-gnome"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundGnome
	["Interface/Transmogrify/TransmogBackgroundGoblin"]={
		["transmog-background-race-goblin"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundGoblin
	["Interface/Transmogrify/TransmogBackgroundHuman"]={
		["transmog-background-race-human"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundHuman
	["Interface/Transmogrify/TransmogBackgroundNightElf"]={
		["transmog-background-race-nightelf"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundNightElf
	["Interface/Transmogrify/TransmogBackgroundOrc"]={
		["transmog-background-race-orc"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundOrc
	["Interface/Transmogrify/TransmogBackgroundPandaren"]={
		["transmog-background-race-pandaren"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundPandaren
	["Interface/Transmogrify/TransmogBackgroundTauren"]={
		["transmog-background-race-tauren"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundTauren
	["Interface/Transmogrify/TransmogBackgroundTroll"]={
		["transmog-background-race-troll"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundTroll
	["Interface/Transmogrify/TransmogBackgroundUndead"]={
		["transmog-background-race-undead"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundUndead
	["Interface/Transmogrify/TransmogBackgroundWorgen"]={
		["transmog-background-race-worgen"]={294, 494, 0.********, 0.576172, 0.********, 0.966797, false, false},
	}, -- Interface/Transmogrify/TransmogBackgroundWorgen
	["Interface/Transmogrify/TransmogHorizontal"]={
		["_transmog-preview-QuestPortrait-Divider_noname"]={128, 44, 0, 1, 0.0078125, 0.351562, true, false},
		["_transmog-preview-QuestPortrait-StoneSwirls-Top"]={128, 18, 0, 1, 0.367188, 0.507812, true, false},
	}, -- Interface/Transmogrify/TransmogHorizontal
	["Interface/Transmogrify/Transmogrify"]={
		["transmog-frame-pink"]={44, 43, 0.466797, 0.552734, 0.********, 0.0859375, false, false},
		["transmog-frame-red"]={44, 43, 0.556641, 0.642578, 0.********, 0.0859375, false, false},
		["transmog-frame-selected"]={62, 62, 0.205078, 0.326172, 0.658203, 0.779297, false, false},
		["transmog-frame"]={58, 57, 0.205078, 0.318359, 0.783203, 0.894531, false, false},
		["transmog-icon-chat"]={13, 13, 0.330078, 0.355469, 0.658203, 0.683594, false, false},
		["transmog-icon-checkmark"]={28, 26, 0.474609, 0.529297, 0.248047, 0.298828, false, false},
		["transmog-icon-remove"]={26, 26, 0.945312, 0.996094, 0.171875, 0.222656, false, false},
		["transmog-icon-revert-small-disabled"]={25, 24, 0.585938, 0.634766, 0.248047, 0.294922, false, false},
		["transmog-icon-revert-small"]={25, 24, 0.533203, 0.582031, 0.248047, 0.294922, false, false},
		["transmog-icon-revert"]={32, 32, 0.878906, 0.941406, 0.171875, 0.234375, false, false},
		["transmog-wardrobe-border-collected"]={96, 122, 0.********, 0.189453, 0.255859, 0.494141, false, false},
		["transmog-wardrobe-border-highlighted"]={84, 110, 0.205078, 0.369141, 0.439453, 0.654297, false, false},
		["transmog-wardrobe-border-selected"]={102, 128, 0.********, 0.201172, 0.********, 0.251953, false, false},
		["transmog-wardrobe-border-uncollected"]={96, 122, 0.********, 0.189453, 0.498047, 0.736328, false, false},
		["transmog-frame-small-pink"]={38, 38, 0.900391, 0.974609, 0.********, 0.0761719, false, false},
		["transmog-frame-small"]={40, 40, 0.736328, 0.814453, 0.********, 0.0800781, false, false},
		["transmog-frame-small-selected"]={40, 40, 0.818359, 0.896484, 0.********, 0.0800781, false, false},
		["transmog-frame-small-red"]={38, 38, 0.373047, 0.447266, 0.09375, 0.167969, false, false},
		["transmog-nav-slot-back"]={35, 37, 0.296875, 0.365234, 0.898438, 0.970703, false, false},
		["transmog-nav-slot-chest"]={35, 37, 0.451172, 0.519531, 0.09375, 0.166016, false, false},
		["transmog-nav-slot-feet"]={35, 37, 0.523438, 0.591797, 0.09375, 0.166016, false, false},
		["transmog-nav-slot-hands"]={35, 37, 0.595703, 0.664062, 0.09375, 0.166016, false, false},
		["transmog-nav-slot-head"]={35, 37, 0.667969, 0.736328, 0.09375, 0.166016, false, false},
		["transmog-nav-slot-legs"]={35, 37, 0.740234, 0.808594, 0.09375, 0.166016, false, false},
		["transmog-nav-slot-mainhand"]={35, 37, 0.8125, 0.880859, 0.09375, 0.166016, false, false},
		["transmog-nav-slot-selected"]={45, 47, 0.205078, 0.292969, 0.898438, 0.990234, false, false},
		["transmog-nav-slot-shirt"]={35, 37, 0.373047, 0.441406, 0.171875, 0.244141, false, false},
		["transmog-nav-slot-shoulder"]={35, 37, 0.445312, 0.513672, 0.171875, 0.244141, false, false},
		["transmog-nav-slot-tabard"]={35, 37, 0.517578, 0.585938, 0.171875, 0.244141, false, false},
		["transmog-nav-slot-waist"]={35, 37, 0.589844, 0.658203, 0.171875, 0.244141, false, false},
		["transmog-nav-slot-wrist"]={35, 37, 0.662109, 0.730469, 0.171875, 0.244141, false, false},
		["transmog-nav-slot-secondaryhand"]={35, 37, 0.884766, 0.953125, 0.09375, 0.166016, false, false},
		["transmog-icon-downarrow"]={15, 9, 0.330078, 0.359375, 0.6875, 0.705078, false, false},
		["transmog-nav-slot-enchant"]={29, 29, 0.414062, 0.470703, 0.248047, 0.304688, false, false},
		["transmog-nav-slot-selected-small"]={34, 35, 0.734375, 0.800781, 0.171875, 0.240234, false, false},
		["transmog-wardrobe-border-unusable"]={96, 122, 0.********, 0.189453, 0.740234, 0.978516, false, false},
		["transmog-icon-hidden"]={36, 30, 0.804688, 0.875, 0.171875, 0.230469, false, false},
		["transmog-frame-blackcover"]={46, 45, 0.373047, 0.462891, 0.********, 0.0898438, false, false},
		["transmog-frame-highlighted"]={44, 41, 0.646484, 0.732422, 0.********, 0.0820312, false, false},
		["transmog-frame-highlighted-small"]={24, 24, 0.322266, 0.369141, 0.783203, 0.830078, false, false},
		["transmog-wardrobe-border-current"]={84, 110, 0.205078, 0.369141, 0.********, 0.216797, false, false},
		["transmog-wardrobe-border-selected-wisp"]={28, 19, 0.638672, 0.693359, 0.248047, 0.285156, false, false},
		["transmog-wardrobe-border-current-transmogged"]={84, 110, 0.205078, 0.369141, 0.220703, 0.435547, false, false},
		["transmog-wardrobe-border-selected-smoke"]={19, 65, 0.373047, 0.410156, 0.248047, 0.375, false, false},
	}, -- Interface/Transmogrify/Transmogrify
	["Interface/Transmogrify/TransmogSets"]={
		["transmog-set-iconrow-background"]={418, 64, 0.********, 0.818359, 0.707031, 0.957031, false, false},
		["transmog-set-model-cutoff-fade"]={403, 178, 0.********, 0.789062, 0.********, 0.699219, false, false},
	}, -- Interface/Transmogrify/TransmogSets
	["Interface/Transmogrify/TransmogSetsVendor"]={
		["transmog-set-border-collected"]={152, 208, 0.********, 0.298828, 0.********, 0.408203, false, false},
		["transmog-set-border-current-transmogged"]={132, 188, 0.587891, 0.845703, 0.********, 0.369141, false, false},
		["transmog-set-border-current"]={132, 188, 0.302734, 0.560547, 0.396484, 0.763672, false, false},
		["transmog-set-border-highlighted"]={132, 188, 0.564453, 0.822266, 0.396484, 0.763672, false, false},
		["transmog-set-border-selected"]={150, 206, 0.********, 0.294922, 0.412109, 0.814453, false, false},
		["transmog-set-border-unusable"]={144, 200, 0.302734, 0.583984, 0.********, 0.392578, false, false},
	}, -- Interface/Transmogrify/TransmogSetsVendor
	["Interface/Transmogrify/TransmogToast"]={
		["transmog-toast-bg"]={253, 75, 0.********, 0.992188, 0.0078125, 0.59375, false, false},
	}, -- Interface/Transmogrify/TransmogToast
	["Interface/WorldMap/Argus/ArgusHighlightZoneAntoran"]={
		["AntoranWastes_Highlight"]={512, 512, 0, 1, 0, 1, true, true},
	}, -- Interface/WorldMap/Argus/ArgusHighlightZoneAntoran
	["Interface/WorldMap/Argus/ArgusHighlightZoneKrokuun"]={
		["Krokuun_Highlight"]={768, 512, 0.*********, 0.750977, 0, 1, false, true},
	}, -- Interface/WorldMap/Argus/ArgusHighlightZoneKrokuun
	["Interface/WorldMap/Argus/ArgusHighlightZoneMacAree"]={
		["MacAree_Highlight"]={768, 512, 0.*********, 0.750977, 0, 1, false, true},
	}, -- Interface/WorldMap/Argus/ArgusHighlightZoneMacAree
	["Interface/WorldMap/Argus/BrokenIslesArgusHighlight"]={
		["BrokenIslesHightlight"]={413, 413, 0.********, 0.808594, 0.********, 0.808594, false, false},
	}, -- Interface/WorldMap/Argus/BrokenIslesArgusHighlight
	["Interface/WorldStateFrame/WorldStateCaptureBar"]={
		["worldstate-capturebar-blue"]={46, 9, 0.6875, 0.867188, 0.0078125, 0.078125, false, false},
		["worldstate-capturebar-gray"]={46, 9, 0.6875, 0.867188, 0.09375, 0.164062, false, false},
		["worldstate-capturebar-green"]={46, 9, 0.6875, 0.867188, 0.226562, 0.296875, false, false},
		["worldstate-capturebar-purple"]={46, 9, 0.6875, 0.867188, 0.3125, 0.382812, false, false},
		["worldstate-capturebar-red"]={46, 9, 0.117188, 0.296875, 0.445312, 0.515625, false, false},
		["worldstate-capturebar-yellow"]={46, 9, 0.304688, 0.484375, 0.445312, 0.515625, false, false},
		["worldstate-capturebar-frame-factions"]={173, 26, 0.********, 0.679688, 0.226562, 0.429688, false, false},
		["worldstate-capturebar-frame"]={173, 26, 0.********, 0.679688, 0.0078125, 0.210938, false, false},
		["worldstate-capturebar-spark-green"]={5, 18, 0.********, 0.0234375, 0.679688, 0.820312, false, false},
		["worldstate-capturebar-spark-yellow"]={5, 18, 0.********, 0.0234375, 0.835938, 0.976562, false, false},
		["worldstate-capturebar-glow"]={27, 28, 0.********, 0.109375, 0.445312, 0.664062, false, false},
		["worldstate-capturebar-arrow"]={9, 15, 0.875, 0.910156, 0.09375, 0.210938, false, false},
		["worldstate-capturebar-frame-separater"]={3, 8, 0.875, 0.886719, 0.0078125, 0.0703125, false, false},
	}, -- Interface/WorldStateFrame/WorldStateCaptureBar
}

return AtlasInfos
