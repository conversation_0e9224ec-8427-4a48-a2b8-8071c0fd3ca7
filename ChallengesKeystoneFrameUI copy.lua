-- ChallengesKeystoneFrameUI.lua
-- 挑战模式钥石框架UI - 从temp.xml转换而来
-- 适用于魔兽世界 3.3.5 版本

-- 本地化变量，提高性能
local _G = _G
local CreateFrame = CreateFrame
local UIParent = UIParent

-- 本地函数声明
local CreateAtlasTexture
local SetupTextureWithAtlas
local CreateChallengesKeystoneFrame
local CreateFrameElements
local CreateOverlayElements
local CreateOverlaySubLevel1
local CreateOverlaySubLevel3
local CreateOverlaySubLevel4

-- 创建Atlas纹理的辅助函数
-- @param parent Frame 父框架
-- @param atlasName string Atlas纹理名称
-- @param layer string 纹理层级 (可选)
-- @param sublevel number 子层级 (可选)
-- @return Texture 创建的纹理对象
CreateAtlasTexture = function(parent, atlasName, layer, sublevel)
    local texture = parent:CreateTexture(nil, layer or "ARTWORK", nil, sublevel or 0)
    
    -- 获取Atlas纹理信息
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        -- 设置纹理路径和坐标
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)
        print("|cff00ff00ChallengesKeystoneFrameUI|r: 成功加载Atlas纹理 '" .. atlasName .. "'")
    else
        print("|cffff0000ChallengesKeystoneFrameUI|r: 警告 - 未找到Atlas纹理 '" .. atlasName .. "'")
    end
    
    return texture
end

-- 设置纹理的Atlas属性
-- @param texture Texture 纹理对象
-- @param atlasName string Atlas纹理名称
-- @param useAtlasSize boolean 是否使用Atlas原始尺寸
SetupTextureWithAtlas = function(texture, atlasName, useAtlasSize)
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)
        if useAtlasSize then
            texture:SetSize(atlasInfo.width, atlasInfo.height)
        end
        return true
    else
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 无法设置Atlas纹理 '" .. atlasName .. "'")
        return false
    end
end

-- 创建挑战模式钥石框架的主函数
-- @return Frame 创建的主框架对象
CreateChallengesKeystoneFrame = function()
    -- 创建主框架
    local frame = CreateFrame("Frame", "ChallengesKeystoneFrame", UIParent)
    frame:SetSize(398, 548)
    frame:SetPoint("CENTER", 0, 40)
    frame:Hide() -- 对应XML中的hidden="true"
    
    print("|cff00ff00ChallengesKeystoneFrameUI|r: 创建主框架 ChallengesKeystoneFrame")
    
    -- BACKGROUND层 - 主背景纹理
    local bgTexture = CreateAtlasTexture(frame, "ChallengeMode-KeystoneFrame", "BACKGROUND")
    bgTexture:SetAllPoints(frame) -- 对应XML中的setAllPoints="true"
    
    -- ARTWORK层 - 符文背景
    local runeBG = CreateAtlasTexture(frame, "ChallengeMode-RuneBG", "ARTWORK")
    runeBG:SetSize(360, 360)
    runeBG:SetPoint("CENTER", 0, 60)
    runeBG:SetAlpha(1)
    frame.RuneBG = runeBG -- 保存引用，供其他元素使用
    
    -- ARTWORK层 - 指令背景
    local instructionBG = frame:CreateTexture(nil, "ARTWORK")
    instructionBG:SetSize(374, 60)
    instructionBG:SetPoint("BOTTOM", 0, 80)
    instructionBG:SetColorTexture(0, 0, 0, 0.8) -- 对应XML中的Color设置
    frame.InstructionBackground = instructionBG
    
    -- ARTWORK层子层级1 - 爆发效果纹理
    local bgBurst = CreateAtlasTexture(frame, "ChallengeMode-Runes-ARTWORKBurst", "ARTWORK", 1)
    SetupTextureWithAtlas(bgBurst, "ChallengeMode-Runes-ARTWORKBurst", true) -- useAtlasSize="true"
    bgBurst:SetPoint("CENTER", runeBG)
    bgBurst:SetAlpha(0)
    bgBurst:SetBlendMode("ADD")
    frame.BgBurst = bgBurst
    
    local bgBurst2 = CreateAtlasTexture(frame, "ChallengeMode-Runes-BackgroundBurst", "ARTWORK", 1)
    SetupTextureWithAtlas(bgBurst2, "ChallengeMode-Runes-BackgroundBurst", true)
    bgBurst2:SetPoint("CENTER", 0, 61)
    bgBurst2:SetAlpha(0)
    bgBurst2:SetBlendMode("ADD")
    frame.BgBurst2 = bgBurst2
    
    local divider = CreateAtlasTexture(frame, "ChallengeMode-ThinDivider", "ARTWORK", 1)
    SetupTextureWithAtlas(divider, "ChallengeMode-ThinDivider", true)
    divider:SetPoint("BOTTOM", instructionBG, "TOP")
    frame.Divider = divider
    
    -- ARTWORK层子层级2 - 符文覆盖发光
    local runeCoverGlow = CreateAtlasTexture(frame, "ChallengeMode-Runes-ARTWORKCoverGlow", "ARTWORK", 2)
    SetupTextureWithAtlas(runeCoverGlow, "ChallengeMode-Runes-ARTWORKCoverGlow", true)
    runeCoverGlow:SetPoint("CENTER", runeBG)
    runeCoverGlow:SetAlpha(0)
    runeCoverGlow:SetBlendMode("BLEND")
    frame.RuneCoverGlow = runeCoverGlow
    
    return frame
end

-- 创建OVERLAY层的字体字符串和纹理元素
-- @param frame Frame 主框架对象
CreateOverlayElements = function(frame)
    -- OVERLAY层 - 字体字符串
    local dungeonName = frame:CreateFontString(nil, "OVERLAY", "QuestFont_Enormous")
    dungeonName:SetSize(350, 0)
    dungeonName:SetPoint("BOTTOM", frame.Divider, "TOP", 0, 4)
    dungeonName:Hide() -- 对应XML中的hidden="true"
    frame.DungeonName = dungeonName
    
    local powerLevel = frame:CreateFontString(nil, "OVERLAY", "QuestFont_Enormous")
    powerLevel:SetPoint("TOP", 0, -30)
    powerLevel:Hide() -- 对应XML中的hidden="true"
    frame.PowerLevel = powerLevel
    
    local timeLimit = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightLarge")
    timeLimit:SetPoint("TOP", frame.Divider, "TOP", 0, -6)
    timeLimit:Hide() -- 对应XML中的hidden="true"
    frame.TimeLimit = timeLimit
    
    local instructions = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightLarge2")
    instructions:SetPoint("CENTER", frame.InstructionBackground)
    instructions:SetText("CHALLENGE_MODE_INSERT_KEYSTONE") -- 对应XML中的text属性
    frame.Instructions = instructions
    
    -- OVERLAY层 - 纹理元素
    local pentagonLines = CreateAtlasTexture(frame, "ChallengeMode-Runes-LineGlow", "OVERLAY")
    SetupTextureWithAtlas(pentagonLines, "ChallengeMode-Runes-LineGlow", true)
    pentagonLines:SetPoint("CENTER", frame.RuneBG, "CENTER", 0, 6)
    pentagonLines:SetAlpha(0)
    frame.PentagonLines = pentagonLines
    
    local largeCircleGlow = CreateAtlasTexture(frame, "ChallengeMode-Runes-InnerCircleGlow", "OVERLAY")
    SetupTextureWithAtlas(largeCircleGlow, "ChallengeMode-Runes-InnerCircleGlow", true)
    largeCircleGlow:SetPoint("CENTER", frame.RuneBG, "CENTER", 0, 5)
    largeCircleGlow:SetAlpha(0)
    frame.LargeCircleGlow = largeCircleGlow
    
    local smallCircleGlow = CreateAtlasTexture(frame, "ChallengeMode-Runes-SmallCircleGlow", "OVERLAY")
    smallCircleGlow:SetSize(130, 130)
    smallCircleGlow:SetPoint("CENTER", frame.RuneBG, "CENTER", 0, 1)
    smallCircleGlow:SetAlpha(0)
    frame.SmallCircleGlow = smallCircleGlow
    
    local shockwave = CreateAtlasTexture(frame, "ChallengeMode-Runes-Shockwave", "OVERLAY")
    SetupTextureWithAtlas(shockwave, "ChallengeMode-Runes-Shockwave", true)
    shockwave:SetPoint("CENTER", 0, 60)
    shockwave:SetAlpha(0)
    shockwave:SetBlendMode("ADD")
    frame.Shockwave = shockwave
    
    local shockwave2 = CreateAtlasTexture(frame, "ChallengeMode-Runes-Shockwave", "OVERLAY")
    SetupTextureWithAtlas(shockwave2, "ChallengeMode-Runes-Shockwave", true)
    shockwave2:SetPoint("CENTER", 0, 60)
    shockwave2:SetAlpha(0)
    shockwave2:SetBlendMode("ADD")
    frame.Shockwave2 = shockwave2
    
    local runesLarge = CreateAtlasTexture(frame, "ChallengeMode-Runes-Large", "OVERLAY")
    runesLarge:SetSize(196, 196)
    runesLarge:SetPoint("CENTER", 0, 61)
    runesLarge:SetAlpha(0.15)
    frame.RunesLarge = runesLarge
    
    local glowBurstLarge = CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowBurstLarge", "OVERLAY")
    SetupTextureWithAtlas(glowBurstLarge, "ChallengeMode-Runes-GlowBurstLarge", true)
    glowBurstLarge:SetPoint("CENTER", frame.RunesLarge, "CENTER", -1, -3)
    glowBurstLarge:SetAlpha(0)
    glowBurstLarge:SetBlendMode("ADD")
    frame.GlowBurstLarge = glowBurstLarge
    
    local runesSmall = CreateAtlasTexture(frame, "ChallengeMode-Runes-Small", "OVERLAY")
    runesSmall:SetSize(125, 125)
    runesSmall:SetPoint("CENTER", 0, 61)
    runesSmall:SetAlpha(0.15)
    frame.RunesSmall = runesSmall
    
    local glowBurstSmall = CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowBurstLarge", "OVERLAY")
    SetupTextureWithAtlas(glowBurstSmall, "ChallengeMode-Runes-GlowBurstLarge", true)
    glowBurstSmall:SetPoint("CENTER", 0, 60)
    glowBurstSmall:SetAlpha(0)
    glowBurstSmall:SetBlendMode("BLEND")
    frame.GlowBurstSmall = glowBurstSmall
end

-- 创建OVERLAY层子层级1的元素
-- @param frame Frame 主框架对象
CreateOverlaySubLevel1 = function(frame)
    -- 钥石槽背景
    local slotBG = CreateAtlasTexture(frame, "ChallengeMode-KeystoneSlotBG", "OVERLAY", 1)
    SetupTextureWithAtlas(slotBG, "ChallengeMode-KeystoneSlotBG", true)
    slotBG:SetPoint("CENTER", 0, 61)
    slotBG:SetAlpha(1)
    frame.SlotBG = slotBG
    
    -- 5个符文圆圈发光效果
    local runeCircleT = CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleT:SetSize(48, 48)
    runeCircleT:SetPoint("CENTER", frame.RuneBG, "CENTER", 0, 126)
    runeCircleT:SetAlpha(0)
    frame.RuneCircleT = runeCircleT
    
    local runeCircleR = CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleR:SetSize(48, 48)
    runeCircleR:SetPoint("CENTER", frame.RuneBG, "CENTER", 118, 40)
    runeCircleR:SetAlpha(0)
    frame.RuneCircleR = runeCircleR
    
    local runeCircleBR = CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleBR:SetSize(48, 48)
    runeCircleBR:SetPoint("CENTER", frame.RuneBG, "CENTER", 73, -98)
    runeCircleBR:SetAlpha(0)
    frame.RuneCircleBR = runeCircleBR
    
    local runeCircleBL = CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleBL:SetSize(48, 48)
    runeCircleBL:SetPoint("CENTER", frame.RuneBG, "CENTER", -73, -98)
    runeCircleBL:SetAlpha(0)
    frame.RuneCircleBL = runeCircleBL
    
    local runeCircleL = CreateAtlasTexture(frame, "ChallengeMode-Runes-CircleGlow", "OVERLAY", 1)
    runeCircleL:SetSize(48, 48)
    runeCircleL:SetPoint("CENTER", frame.RuneBG, "CENTER", -118, 40)
    runeCircleL:SetAlpha(0)
    frame.RuneCircleL = runeCircleL
end

-- 创建OVERLAY层子层级3的元素
-- @param frame Frame 主框架对象
CreateOverlaySubLevel3 = function(frame)
    -- 钥石框架
    local keystoneFrame = CreateAtlasTexture(frame, "ChallengeMode-KeystoneSlotFrame", "OVERLAY", 3)
    SetupTextureWithAtlas(keystoneFrame, "ChallengeMode-KeystoneSlotFrame", true)
    keystoneFrame:SetPoint("CENTER", 0, 61)
    keystoneFrame:SetAlpha(1)
    frame.KeystoneFrame = keystoneFrame

    -- 各个方向的符文发光效果
    local runeT = CreateAtlasTexture(frame, "ChallengeMode-Runes-T-Glow", "OVERLAY", 3)
    runeT:SetSize(40, 40)
    runeT:SetPoint("CENTER", frame.RuneCircleT)
    runeT:SetAlpha(0)
    frame.RuneT = runeT

    local runeR = CreateAtlasTexture(frame, "ChallengeMode-Runes-R-Glow", "OVERLAY", 3)
    runeR:SetSize(40, 40)
    runeR:SetPoint("CENTER", frame.RuneCircleR, "CENTER", -1, 0)
    runeR:SetAlpha(0)
    frame.RuneR = runeR

    local runeBR = CreateAtlasTexture(frame, "ChallengeMode-Runes-BR-Glow", "OVERLAY", 3)
    runeBR:SetSize(40, 40)
    runeBR:SetPoint("CENTER", frame.RuneCircleBR, "CENTER", -1, 0)
    runeBR:SetAlpha(0)
    frame.RuneBR = runeBR

    local runeBL = CreateAtlasTexture(frame, "ChallengeMode-Runes-BL-Glow", "OVERLAY", 3)
    runeBL:SetSize(40, 40)
    runeBL:SetPoint("CENTER", frame.RuneCircleBL, "CENTER", -1, 0)
    runeBL:SetAlpha(0)
    frame.RuneBL = runeBL

    local runeL = CreateAtlasTexture(frame, "ChallengeMode-Runes-L-Glow", "OVERLAY", 3)
    runeL:SetSize(40, 40)
    runeL:SetPoint("CENTER", frame.RuneCircleL)
    runeL:SetAlpha(0)
    frame.RuneL = runeL

    -- 大符文发光
    local largeRuneGlow = CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowLarge", "OVERLAY", 3)
    largeRuneGlow:SetSize(198, 199)
    largeRuneGlow:SetPoint("CENTER", 0, 61)
    largeRuneGlow:SetAlpha(0)
    largeRuneGlow:SetBlendMode("ADD")
    frame.LargeRuneGlow = largeRuneGlow

    -- 小符文发光
    local smallRuneGlow = CreateAtlasTexture(frame, "ChallengeMode-Runes-GlowSmall", "OVERLAY", 3)
    smallRuneGlow:SetSize(129, 129)
    smallRuneGlow:SetPoint("CENTER", 0, 61)
    smallRuneGlow:SetAlpha(0)
    smallRuneGlow:SetBlendMode("ADD")
    frame.SmallRuneGlow = smallRuneGlow
end

-- 创建OVERLAY层子层级4的元素
-- @param frame Frame 主框架对象
CreateOverlaySubLevel4 = function(frame)
    -- 钥石槽发光效果
    local keystoneSlotGlow = CreateAtlasTexture(frame, "ChallengeMode-KeystoneSlotFrameGlow", "OVERLAY", 4)
    SetupTextureWithAtlas(keystoneSlotGlow, "ChallengeMode-KeystoneSlotFrameGlow", true)
    keystoneSlotGlow:SetPoint("CENTER", 0, 60)
    keystoneSlotGlow:SetAlpha(0)
    keystoneSlotGlow:SetBlendMode("ADD")
    frame.KeystoneSlotGlow = keystoneSlotGlow
end

-- 创建框架内的子框架元素（按钮等）
-- @param frame Frame 主框架对象
CreateFrameElements = function(frame)
    -- 关闭按钮
    local closeButton = CreateFrame("Button", nil, frame, "UIPanelCloseButton")
    closeButton:SetPoint("TOPRIGHT", -4, -4)
    closeButton:SetScript("OnClick", function(self)
        self:GetParent():Hide()
    end)
    frame.CloseButton = closeButton

    -- 激活按钮
    local startButton = CreateFrame("Button", nil, frame, "UIPanelButtonTemplate")
    startButton:SetSize(120, 24)
    startButton:SetPoint("BOTTOM", 0, 20)
    startButton:SetText("激活")
    startButton:Disable() -- 对应XML中的enabled="false"
    startButton:SetScript("OnShow", function(self)
        self:SetWidth(self:GetTextWidth() + 60)
    end)
    frame.StartButton = startButton

    -- 钥石槽按钮
    local keystoneSlot = CreateFrame("Button", nil, frame)
    keystoneSlot:SetSize(48, 48)
    keystoneSlot:SetPoint("CENTER", frame.SlotBG)
    keystoneSlot:EnableMouse(true)

    -- 钥石槽纹理
    local keystoneTexture = keystoneSlot:CreateTexture(nil, "OVERLAY", nil, 2)
    keystoneTexture:SetAllPoints(keystoneSlot)
    keystoneSlot.Texture = keystoneTexture

    -- 钥石槽事件处理
    keystoneSlot:SetScript("OnLeave", function()
        if GameTooltip then
            GameTooltip:Hide()
        end
    end)

    frame.KeystoneSlot = keystoneSlot

    -- 创建隐藏的词缀框架（对应XML中的ChallengesKeystoneFrameAffixTemplate）
    local affixFrame = CreateFrame("Frame", nil, frame)
    affixFrame:Hide() -- 对应XML中的hidden="true"
    frame.AffixFrame = affixFrame
end

-- 初始化UI的主函数
-- @return Frame 创建的框架对象
function InitializeUI()
    -- 检查ExtractAtlasInfos插件是否可用
    if not GetAtlasTextureInfo then
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - ExtractAtlasInfos插件未加载或GetAtlasTextureInfo函数不可用")
        return nil
    end

    local frame = CreateChallengesKeystoneFrame()
    if frame then
        CreateOverlayElements(frame)
        CreateOverlaySubLevel1(frame)
        CreateOverlaySubLevel3(frame)
        CreateOverlaySubLevel4(frame)
        CreateFrameElements(frame)

        print("|cff00ff00ChallengesKeystoneFrameUI|r: 挑战模式钥石框架创建完成")
        return frame
    else
        print("|cffff0000ChallengesKeystoneFrameUI|r: 错误 - 无法创建主框架")
        return nil
    end
end

-- 全局函数：创建并显示挑战模式钥石框架
-- @return Frame 创建的框架对象
function CreateAndShowChallengesKeystoneFrame()
    local frame = InitializeUI()
    if frame then
        frame:Show()
        print("|cff00ff00ChallengesKeystoneFrameUI|r: 挑战模式钥石框架已显示")
        return frame
    end
    return nil
end

-- 测试命令：创建并显示UI
-- 使用方法：在聊天框中输入 /script TestChallengesKeystoneFrame()
function TestChallengesKeystoneFrame()
    local frame = CreateAndShowChallengesKeystoneFrame()
    if frame then
        print("|cff00ff00ChallengesKeystoneFrameUI|r: 测试UI已创建，使用 /script ChallengesKeystoneFrame:Hide() 来隐藏")
    end
end

print("|cff00ff00ChallengesKeystoneFrameUI|r: 脚本已加载完成")
